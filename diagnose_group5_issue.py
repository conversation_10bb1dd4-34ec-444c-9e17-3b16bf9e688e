#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断第5组（最高波动率组合）数据异常问题

功能：
1. 检查第5组的原始收益数据
2. 分析导致NaN值的原因
3. 提供修复建议

作者: AI Assistant
创建时间: 2025年1月
"""

import pandas as pd
import numpy as np
import warnings
import os

warnings.filterwarnings('ignore')

def load_backtest_data():
    """加载回测数据"""
    print("🔍 加载回测数据进行诊断...")
    
    # 重新运行简化版的波动率因子回测，只关注数据收集
    from hsi_volatility_factor_backtest import HSIVolatilityFactorBacktest
    
    backtest = HSIVolatilityFactorBacktest()
    
    # 加载价格数据
    if not backtest.load_all_price_data():
        print("❌ 价格数据加载失败")
        return None
    
    # 计算波动率
    volatility_df = backtest.calculate_rolling_volatility(
        start_date="2021-01-01", 
        end_date="2024-12-31"
    )
    
    # 运行回测
    results = backtest.run_volatility_factor_backtest(
        start_date="2021-01-01", 
        end_date="2024-12-31", 
        n_groups=5
    )
    
    return backtest

def analyze_group5_data(backtest):
    """分析第5组数据"""
    if backtest is None:
        return
    
    print("\n🔍 分析第5组（最高波动率组合）数据...")
    
    portfolio_returns = backtest.backtest_results['portfolio_returns']
    portfolio_holdings = backtest.backtest_results['portfolio_holdings']
    
    group5_returns = portfolio_returns.get('Group_5_LowVol', pd.DataFrame())
    group5_holdings = portfolio_holdings.get('Group_5_LowVol', [])
    
    if group5_returns.empty:
        print("❌ 第5组收益数据为空")
        return
    
    print(f"📊 第5组基本信息:")
    print(f"   - 调仓期数: {len(group5_returns)}")
    print(f"   - 持仓记录数: {len(group5_holdings)}")
    
    # 分析收益率数据
    returns = group5_returns['return'].values
    print(f"\n📈 收益率统计:")
    print(f"   - 收益率数量: {len(returns)}")
    print(f"   - 平均收益率: {np.mean(returns):.4f}")
    print(f"   - 收益率标准差: {np.std(returns):.4f}")
    print(f"   - 最小收益率: {np.min(returns):.4f}")
    print(f"   - 最大收益率: {np.max(returns):.4f}")
    print(f"   - 中位数收益率: {np.median(returns):.4f}")
    
    # 检查极端值
    extreme_negative = returns[returns < -0.5]  # 小于-50%的收益率
    extreme_positive = returns[returns > 1.0]   # 大于100%的收益率
    
    print(f"\n⚠️  极端值检查:")
    print(f"   - 小于-50%的收益率: {len(extreme_negative)} 个")
    if len(extreme_negative) > 0:
        print(f"     最极端负收益: {np.min(extreme_negative):.4f}")
        print(f"     极端负收益列表: {extreme_negative}")
    
    print(f"   - 大于100%的收益率: {len(extreme_positive)} 个")
    if len(extreme_positive) > 0:
        print(f"     最极端正收益: {np.max(extreme_positive):.4f}")
        print(f"     极端正收益列表: {extreme_positive}")
    
    # 检查NaN和无穷值
    nan_count = np.sum(np.isnan(returns))
    inf_count = np.sum(np.isinf(returns))
    
    print(f"\n🔍 数据质量检查:")
    print(f"   - NaN值数量: {nan_count}")
    print(f"   - 无穷值数量: {inf_count}")
    
    # 分析累积收益计算
    print(f"\n📊 累积收益计算分析:")
    
    # 检查 1 + returns 的值
    one_plus_returns = 1 + returns
    negative_values = one_plus_returns[one_plus_returns <= 0]
    
    print(f"   - 1+收益率中 <= 0 的值: {len(negative_values)} 个")
    if len(negative_values) > 0:
        print(f"     最小的1+收益率: {np.min(one_plus_returns):.6f}")
        print(f"     负值列表: {negative_values}")
    
    # 尝试计算累积收益
    try:
        total_return = np.prod(one_plus_returns) - 1
        print(f"   - 总收益率: {total_return:.6f}")
        
        if np.isnan(total_return) or np.isinf(total_return):
            print(f"   ⚠️  总收益率为 {total_return}，这解释了为什么年化收益率为NaN")
        
    except Exception as e:
        print(f"   ❌ 计算总收益率失败: {e}")
    
    # 分析具体的问题期间
    print(f"\n📅 问题期间分析:")
    for i, (_, row) in enumerate(group5_returns.iterrows()):
        return_val = row['return']
        if return_val < -0.8:  # 收益率小于-80%
            date = row['date']
            stocks_count = row['stocks_count']
            print(f"   - {date.strftime('%Y-%m-%d')}: 收益率 {return_val:.4f}, 持股数 {stocks_count}")
            
            # 查看该期间的持仓
            if i < len(group5_holdings):
                holding = group5_holdings[i]
                avg_volatility = holding.get('avg_volatility', 'N/A')
                print(f"     平均波动率: {avg_volatility}")

def suggest_fixes():
    """提供修复建议"""
    print(f"\n💡 修复建议:")
    print("-" * 40)
    print("1. 数据清洗:")
    print("   - 过滤极端收益率（如 < -95% 或 > 500%）")
    print("   - 检查数据源质量，可能存在错误数据")
    print("")
    print("2. 计算方法改进:")
    print("   - 使用对数收益率计算累积收益")
    print("   - 添加异常值检测和处理")
    print("   - 使用更稳健的统计方法")
    print("")
    print("3. 风险控制:")
    print("   - 设置单期最大损失限制")
    print("   - 排除流动性极差的股票")
    print("   - 增加基本面筛选条件")
    print("")
    print("4. 技术改进:")
    print("   - 在计算前检查数据有效性")
    print("   - 使用try-catch处理异常情况")
    print("   - 提供更详细的错误信息")

def main():
    """主函数"""
    print("🚀 第5组数据异常诊断")
    print("=" * 50)
    
    # 1. 加载数据
    backtest = load_backtest_data()
    
    # 2. 分析第5组数据
    analyze_group5_data(backtest)
    
    # 3. 提供修复建议
    suggest_fixes()
    
    print(f"\n✅ 诊断完成")

if __name__ == "__main__":
    main()

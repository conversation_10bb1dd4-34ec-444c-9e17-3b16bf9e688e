"""
简化版营收因子有效性测试

使用akshare获取港股实时数据，结合营收数据进行简单的因子有效性测试
"""

import pandas as pd
import numpy as np
import akshare as ak
import time
import warnings
warnings.filterwarnings('ignore')

def test_revenue_factor():
    """测试营收因子有效性"""
    
    print("🚀 营收因子有效性测试")
    print("="*50)
    
    # 1. 加载营收数据
    print("📊 加载营收数据...")
    revenue_file = "data/hsi_quarterly_revenue_data/hsi_all_quarterly_revenue.csv"
    
    try:
        revenue_df = pd.read_csv(revenue_file)
        revenue_df['report_date'] = pd.to_datetime(revenue_df['report_date'])
        
        # 获取最新的营收数据
        latest_date = revenue_df['report_date'].max()
        latest_revenue = revenue_df[revenue_df['report_date'] == latest_date].copy()
        
        print(f"✅ 营收数据加载成功")
        print(f"📅 最新财报日期: {latest_date.strftime('%Y-%m-%d')}")
        print(f"🏢 股票数量: {len(latest_revenue)}")
        
    except Exception as e:
        print(f"❌ 营收数据加载失败: {e}")
        return
    
    # 2. 获取港股实时数据
    print("\n📈 获取港股实时价格数据...")
    
    try:
        # 获取港股实时行情
        hk_spot = ak.stock_hk_spot_em()
        print(f"✅ 获取到 {len(hk_spot)} 只港股实时数据")
        
        # 格式化股票代码进行匹配
        hk_spot['stock_code'] = hk_spot['代码'].str.replace('.HK', '').str.zfill(5)
        
    except Exception as e:
        print(f"❌ 港股数据获取失败: {e}")
        return
    
    # 3. 合并营收数据和价格数据
    print("\n🔗 合并营收和价格数据...")
    
    # 合并数据
    merged_data = latest_revenue.merge(
        hk_spot[['stock_code', '最新价', '涨跌幅', '市值']],
        on='stock_code',
        how='inner'
    )
    
    print(f"✅ 成功匹配 {len(merged_data)} 只股票")
    
    if len(merged_data) < 10:
        print("⚠️  匹配的股票数量太少，无法进行有效分析")
        return
    
    # 4. 计算营收因子得分
    print("\n🧮 计算营收因子...")
    
    # 营收增长率因子
    growth_data = merged_data.dropna(subset=['revenue_yoy']).copy()
    
    if len(growth_data) >= 10:
        # 标准化营收增长率
        growth_data['growth_score'] = (
            growth_data['revenue_yoy'] - growth_data['revenue_yoy'].mean()
        ) / growth_data['revenue_yoy'].std()
        
        # 营收规模因子
        growth_data['size_score'] = (
            np.log(growth_data['revenue']) - np.log(growth_data['revenue']).mean()
        ) / np.log(growth_data['revenue']).std()
        
        print(f"✅ 计算完成，有效股票: {len(growth_data)}")
        
        # 5. 分析因子与当前表现的关系
        print("\n📊 分析因子有效性...")
        
        # 计算相关性
        growth_corr = growth_data['growth_score'].corr(growth_data['涨跌幅'])
        size_corr = growth_data['size_score'].corr(growth_data['涨跌幅'])
        
        print(f"📈 营收增长率因子与当日涨跌幅相关性: {growth_corr:.4f}")
        print(f"💰 营收规模因子与当日涨跌幅相关性: {size_corr:.4f}")
        
        # 分组分析
        print("\n📋 分组分析结果:")
        
        # 按营收增长率分组
        growth_data['growth_quintile'] = pd.qcut(
            growth_data['growth_score'], 
            q=3, 
            labels=['低增长', '中增长', '高增长']
        )
        
        growth_group_performance = growth_data.groupby('growth_quintile')['涨跌幅'].agg(['mean', 'count'])
        
        print("营收增长率分组表现:")
        for group in growth_group_performance.index:
            mean_return = growth_group_performance.loc[group, 'mean']
            count = growth_group_performance.loc[group, 'count']
            print(f"  {group}: 平均涨跌幅 {mean_return:.2f}% (样本数: {count})")
        
        # 按营收规模分组
        growth_data['size_quintile'] = pd.qcut(
            growth_data['size_score'], 
            q=3, 
            labels=['小规模', '中规模', '大规模']
        )
        
        size_group_performance = growth_data.groupby('size_quintile')['涨跌幅'].agg(['mean', 'count'])
        
        print("\n营收规模分组表现:")
        for group in size_group_performance.index:
            mean_return = size_group_performance.loc[group, 'mean']
            count = size_group_performance.loc[group, 'count']
            print(f"  {group}: 平均涨跌幅 {mean_return:.2f}% (样本数: {count})")
        
        # 6. 展示具体案例
        print("\n🎯 具体案例分析:")
        
        # 高增长股票
        high_growth = growth_data[growth_data['growth_quintile'] == '高增长'].nlargest(3, 'revenue_yoy')
        print("高营收增长率股票:")
        for _, stock in high_growth.iterrows():
            print(f"  {stock['stock_code']} ({stock['stock_name']}): "
                  f"增长率 {stock['revenue_yoy']:.1f}%, 当日涨跌 {stock['涨跌幅']:.2f}%")
        
        # 低增长股票
        low_growth = growth_data[growth_data['growth_quintile'] == '低增长'].nsmallest(3, 'revenue_yoy')
        print("\n低营收增长率股票:")
        for _, stock in low_growth.iterrows():
            print(f"  {stock['stock_code']} ({stock['stock_name']}): "
                  f"增长率 {stock['revenue_yoy']:.1f}%, 当日涨跌 {stock['涨跌幅']:.2f}%")
        
        # 7. 结论
        print("\n" + "="*50)
        print("📋 营收因子有效性结论:")
        
        # 基于相关性判断
        if abs(growth_corr) > 0.2:
            growth_conclusion = "✅ 营收增长率因子显示出较强的预测能力"
        elif abs(growth_corr) > 0.1:
            growth_conclusion = "⚠️  营收增长率因子显示出一定的预测能力"
        else:
            growth_conclusion = "❌ 营收增长率因子预测能力较弱"
        
        if abs(size_corr) > 0.2:
            size_conclusion = "✅ 营收规模因子显示出较强的预测能力"
        elif abs(size_corr) > 0.1:
            size_conclusion = "⚠️  营收规模因子显示出一定的预测能力"
        else:
            size_conclusion = "❌ 营收规模因子预测能力较弱"
        
        print(f"📈 {growth_conclusion}")
        print(f"💰 {size_conclusion}")
        
        # 基于分组分析判断
        high_low_diff = (
            growth_group_performance.loc['高增长', 'mean'] - 
            growth_group_performance.loc['低增长', 'mean']
        )
        
        print(f"\n🔄 高增长组 vs 低增长组差异: {high_low_diff:.2f}%")
        
        if high_low_diff > 1:
            strategy_conclusion = "✅ 营收增长率因子适合构建投资策略"
        elif high_low_diff > 0:
            strategy_conclusion = "⚠️  营收增长率因子可考虑结合其他因子使用"
        else:
            strategy_conclusion = "❌ 营收增长率因子不适合单独构建策略"
        
        print(f"💡 {strategy_conclusion}")
        
        # 保存结果
        try:
            output_dir = "data/hsi_revenue_factor_test"
            import os
            os.makedirs(output_dir, exist_ok=True)
            
            # 保存详细数据
            growth_data.to_csv(f"{output_dir}/revenue_factor_analysis.csv", index=False, encoding='utf-8-sig')
            
            # 保存总结报告
            with open(f"{output_dir}/revenue_factor_test_summary.txt", 'w', encoding='utf-8') as f:
                f.write("营收因子有效性测试报告\n")
                f.write("="*30 + "\n\n")
                f.write(f"测试时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"样本数量: {len(growth_data)}\n")
                f.write(f"营收增长率相关性: {growth_corr:.4f}\n")
                f.write(f"营收规模相关性: {size_corr:.4f}\n")
                f.write(f"高低增长组差异: {high_low_diff:.2f}%\n\n")
                f.write(f"结论:\n")
                f.write(f"- {growth_conclusion}\n")
                f.write(f"- {size_conclusion}\n")
                f.write(f"- {strategy_conclusion}\n")
            
            print(f"\n📁 结果已保存到: {output_dir}")
            
        except Exception as e:
            print(f"⚠️  保存结果时出错: {e}")
        
    else:
        print("❌ 有效的营收增长率数据不足，无法进行分析")

if __name__ == "__main__":
    test_revenue_factor()

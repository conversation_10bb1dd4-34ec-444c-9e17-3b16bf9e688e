#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
恒生指数反向毛利率因子回测工具

功能：
1. 反向毛利率因子回测（低毛利率为正向信号）
2. 多种反向策略测试
3. 分组回测分析
4. 与正向毛利率因子对比
5. 可视化结果展示

作者: AI Assistant
创建时间: 2025年1月
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
import os
import time
from typing import Dict, List, Tuple, Optional
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import plotly.offline as pyo

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class HSIReverseGrossMarginFactorBacktest:
    """恒生指数反向毛利率因子回测器"""
    
    def __init__(self, gross_margin_file: str = None, price_cache_dir: str = "hsi_price_cache"):
        self.gross_margin_file = gross_margin_file or "data/hsi_quarterly_gross_margin_data/hsi_all_quarterly_gross_margin.csv"
        self.price_cache_dir = price_cache_dir
        self.gross_margin_data = None
        self.price_data = {}
        self.backtest_results = {}
        self.rebalance_freq = 'Q'  # 季度调仓（匹配财务数据频率）
        
        # 创建价格缓存目录
        os.makedirs(price_cache_dir, exist_ok=True)
        
    def load_gross_margin_data(self) -> bool:
        """加载毛利率数据"""
        try:
            print(f"📊 加载毛利率数据进行反向因子回测...")
            self.gross_margin_data = pd.read_csv(self.gross_margin_file, parse_dates=['report_date'])
            
            # 数据预处理
            self.gross_margin_data = self.gross_margin_data.sort_values(['stock_code', 'report_date']).reset_index(drop=True)
            
            # 过滤有效的毛利率数据
            self.gross_margin_data = self.gross_margin_data.dropna(subset=['gross_margin']).copy()
            
            # 过滤异常值（毛利率在-100%到200%之间）
            self.gross_margin_data = self.gross_margin_data[
                (self.gross_margin_data['gross_margin'] >= -1.0) & 
                (self.gross_margin_data['gross_margin'] <= 2.0)
            ].copy()
            
            print(f"✅ 毛利率数据加载成功")
            print(f"   - 总记录数: {len(self.gross_margin_data):,}")
            print(f"   - 股票数量: {self.gross_margin_data['stock_code'].nunique()}")
            print(f"   - 日期范围: {self.gross_margin_data['report_date'].min().date()} 到 {self.gross_margin_data['report_date'].max().date()}")
            
            return True
            
        except Exception as e:
            print(f"❌ 毛利率数据加载失败: {e}")
            return False
    
    def get_stock_price_data(self, stock_code: str) -> pd.DataFrame:
        """获取单只股票的价格数据（复用ROE回测的缓存）"""
        # 确保股票代码是5位数格式
        stock_code_formatted = str(stock_code).zfill(5)
        # 使用与ROE回测相同的缓存格式
        cache_file = os.path.join(self.price_cache_dir, f"{stock_code_formatted}_price.csv")
        
        # 检查缓存
        if os.path.exists(cache_file):
            try:
                cached_data = pd.read_csv(cache_file, parse_dates=['date'])
                if not cached_data.empty:
                    print(f"📦 使用ROE回测缓存数据: {stock_code_formatted}")
                    # 转换为与原格式兼容的DataFrame
                    cached_data['date'] = pd.to_datetime(cached_data['date'])
                    cached_data.set_index('date', inplace=True)
                    return cached_data
            except Exception as e:
                print(f"⚠️  读取缓存失败 {stock_code_formatted}: {e}")
        
        print(f"⚠️  {stock_code_formatted} 无缓存数据，跳过")
        return pd.DataFrame()
    
    def load_all_price_data(self) -> bool:
        """加载所有股票的价格数据（从ROE回测缓存）"""
        if self.gross_margin_data is None:
            print("❌ 请先加载毛利率数据")
            return False
        
        unique_stocks = self.gross_margin_data['stock_code'].unique()
        print(f"📥 开始加载 {len(unique_stocks)} 只股票的价格数据（从ROE回测缓存）...")
        
        success_count = 0
        cached_count = 0
        
        for i, stock_code in enumerate(unique_stocks):
            print(f"📊 处理 {stock_code} ({i+1}/{len(unique_stocks)})")
            
            price_data = self.get_stock_price_data(stock_code)
            if not price_data.empty:
                self.price_data[stock_code] = price_data
                success_count += 1
                cached_count += 1
        
        print(f"✅ 价格数据加载完成:")
        print(f"   - 成功加载: {success_count}/{len(unique_stocks)}")
        print(f"   - 使用缓存: {cached_count}")
        
        return success_count > 0
    
    def prepare_backtest_data(self, start_date: str = "2021-01-01", end_date: str = "2024-12-31") -> pd.DataFrame:
        """准备回测数据"""
        print(f"\n📅 准备反向毛利率因子回测数据: {start_date} 到 {end_date}")
        
        # 筛选日期范围
        start_date = pd.to_datetime(start_date)
        end_date = pd.to_datetime(end_date)
        
        backtest_data = self.gross_margin_data[
            (self.gross_margin_data['report_date'] >= start_date) & 
            (self.gross_margin_data['report_date'] <= end_date)
        ].copy()
        
        # 生成季度调仓日期（考虑财务报告发布时间）
        # 港股财务报告通常在季度结束后1-2个月发布
        # 调仓时间设为每季度第二个月的月初（2月、5月、8月、11月）
        rebalance_dates = pd.date_range(
            start=start_date,
            end=end_date,
            freq='QS'  # 先生成季度开始日期
        )

        # 调整为财务报告发布后的合理调仓时间
        adjusted_rebalance_dates = []
        for date in rebalance_dates:
            # 将调仓时间推迟到下个月月初（给财务报告发布留时间）
            if date.month in [1, 4, 7, 10]:  # Q1, Q2, Q3, Q4开始
                adjusted_month = date.month + 1
                adjusted_year = date.year
                if adjusted_month > 12:
                    adjusted_month = 1
                    adjusted_year += 1
                adjusted_date = pd.Timestamp(adjusted_year, adjusted_month, 1)
                adjusted_rebalance_dates.append(adjusted_date)

        rebalance_dates = pd.DatetimeIndex(adjusted_rebalance_dates)
        
        print(f"   - 回测期间记录数: {len(backtest_data):,}")
        print(f"   - 调仓次数: {len(rebalance_dates)}")
        
        return backtest_data, rebalance_dates
    
    def calculate_reverse_gross_margin_rankings(self, data: pd.DataFrame, date: pd.Timestamp) -> pd.DataFrame:
        """计算指定日期的反向毛利率排名（低毛利率排名靠前）"""
        # 获取该季度的毛利率数据
        quarter_start = pd.Timestamp(date.year, ((date.month-1)//3)*3+1, 1)
        
        # 查找最近的财务报告数据
        available_data = data[data['report_date'] <= date]
        if available_data.empty:
            return pd.DataFrame()
        
        # 获取每只股票最新的毛利率数据
        latest_data = available_data.groupby('stock_code').last().reset_index()
        
        # 计算反向毛利率排名（低毛利率排名靠前）
        latest_data = latest_data.sort_values('gross_margin', ascending=True).reset_index(drop=True)  # 改为升序
        latest_data['reverse_gross_margin_rank'] = range(1, len(latest_data) + 1)
        latest_data['reverse_gross_margin_percentile'] = latest_data['reverse_gross_margin_rank'] / len(latest_data)
        
        return latest_data
    
    def create_reverse_gross_margin_portfolios(self, ranked_data: pd.DataFrame, n_groups: int = 5) -> Dict[str, List[str]]:
        """创建反向毛利率分组投资组合（低毛利率为Group 1）"""
        if ranked_data.empty:
            return {}
        
        portfolios = {}
        group_size = len(ranked_data) // n_groups
        
        for i in range(n_groups):
            start_idx = i * group_size
            if i == n_groups - 1:  # 最后一组包含剩余所有股票
                end_idx = len(ranked_data)
            else:
                end_idx = (i + 1) * group_size
            
            group_stocks = ranked_data.iloc[start_idx:end_idx]['stock_code'].tolist()
            portfolios[f'Group_{i+1}_ReverseLowGM'] = group_stocks
        
        return portfolios
    
    def calculate_portfolio_return(self, stocks: List[str], start_date: pd.Timestamp, 
                                 end_date: pd.Timestamp) -> float:
        """计算投资组合收益率"""
        try:
            portfolio_returns = []
            
            for stock_code in stocks:
                if stock_code not in self.price_data:
                    continue
                
                price_data = self.price_data[stock_code]
                
                # 获取期间价格数据
                period_data = price_data[
                    (price_data.index >= start_date) & 
                    (price_data.index <= end_date)
                ]
                
                if len(period_data) < 2:
                    continue
                
                # 计算期间收益率
                start_price = period_data['close'].iloc[0]
                end_price = period_data['close'].iloc[-1]
                
                if start_price > 0:
                    stock_return = (end_price - start_price) / start_price
                    portfolio_returns.append(stock_return)
            
            # 等权重平均
            if portfolio_returns:
                return np.mean(portfolio_returns)
            else:
                return 0.0
                
        except Exception as e:
            print(f"⚠️  计算组合收益失败: {e}")
            return 0.0
    
    def simulate_reverse_portfolio_returns(self, backtest_data: pd.DataFrame, rebalance_dates: pd.DatetimeIndex, 
                                         n_groups: int = 5) -> Dict[str, pd.DataFrame]:
        """模拟反向毛利率投资组合收益"""
        print(f"\n🔄 开始反向毛利率因子回测模拟...")
        
        portfolio_returns = {f'Group_{i+1}_ReverseLowGM': [] for i in range(n_groups)}
        portfolio_holdings = {f'Group_{i+1}_ReverseLowGM': [] for i in range(n_groups)}
        
        for i, rebalance_date in enumerate(rebalance_dates[:-1]):
            next_rebalance = rebalance_dates[i + 1]
            
            print(f"📊 处理调仓期间: {rebalance_date.date()} 到 {next_rebalance.date()}")
            
            # 获取调仓日的反向毛利率排名
            ranked_data = self.calculate_reverse_gross_margin_rankings(backtest_data, rebalance_date)
            
            if ranked_data.empty:
                print(f"⚠️  {rebalance_date.date()} 无可用数据，跳过")
                continue
            
            # 创建投资组合
            portfolios = self.create_reverse_gross_margin_portfolios(ranked_data, n_groups)
            
            # 计算持有期收益
            for group_name, stocks in portfolios.items():
                if not stocks:
                    continue
                
                # 计算组合收益（等权重）
                period_return = self.calculate_portfolio_return(stocks, rebalance_date, next_rebalance)
                
                portfolio_returns[group_name].append({
                    'date': rebalance_date,
                    'return': period_return,
                    'stocks_count': len(stocks)
                })
                
                portfolio_holdings[group_name].append({
                    'date': rebalance_date,
                    'stocks': stocks,
                    'avg_gross_margin': ranked_data[ranked_data['stock_code'].isin(stocks)]['gross_margin'].mean()
                })
        
        # 转换为DataFrame
        results = {}
        for group_name in portfolio_returns:
            if portfolio_returns[group_name]:
                results[group_name] = pd.DataFrame(portfolio_returns[group_name])
            else:
                results[group_name] = pd.DataFrame()
        
        self.backtest_results['portfolio_returns'] = results
        self.backtest_results['portfolio_holdings'] = portfolio_holdings
        
        return results

    def calculate_performance_metrics(self) -> pd.DataFrame:
        """计算绩效指标"""
        print(f"\n📈 计算反向毛利率因子绩效指标...")

        if 'portfolio_returns' not in self.backtest_results:
            print("❌ 请先运行回测模拟")
            return pd.DataFrame()

        portfolio_returns = self.backtest_results['portfolio_returns']
        metrics = []

        for group_name, returns_df in portfolio_returns.items():
            if returns_df.empty:
                continue

            returns = returns_df['return'].values

            # 基本统计
            total_return = np.prod(1 + returns) - 1
            annualized_return = (1 + total_return) ** (4 / len(returns)) - 1  # 季度调仓，年化
            volatility = np.std(returns) * np.sqrt(4)  # 季度波动率年化
            sharpe_ratio = annualized_return / volatility if volatility > 0 else 0

            # 最大回撤
            cumulative_returns = np.cumprod(1 + returns)
            running_max = np.maximum.accumulate(cumulative_returns)
            drawdowns = (cumulative_returns - running_max) / running_max
            max_drawdown = np.min(drawdowns)

            # 胜率
            win_rate = np.sum(returns > 0) / len(returns) if len(returns) > 0 else 0

            metrics.append({
                'Portfolio': group_name,
                'Total_Return': total_return,
                'Annualized_Return': annualized_return,
                'Volatility': volatility,
                'Sharpe_Ratio': sharpe_ratio,
                'Max_Drawdown': max_drawdown,
                'Win_Rate': win_rate,
                'Periods': len(returns)
            })

        metrics_df = pd.DataFrame(metrics)
        self.backtest_results['performance_metrics'] = metrics_df

        return metrics_df

    def analyze_reverse_factor_effectiveness(self) -> Dict:
        """分析反向毛利率因子有效性"""
        print(f"\n🔍 分析反向毛利率因子有效性...")

        if 'performance_metrics' not in self.backtest_results:
            self.calculate_performance_metrics()

        metrics_df = self.backtest_results['performance_metrics']

        if metrics_df.empty:
            return {}

        # 分析低毛利率组合 vs 高毛利率组合
        low_margin_group = metrics_df[metrics_df['Portfolio'] == 'Group_1_ReverseLowGM']  # 最低毛利率
        high_margin_group = metrics_df[metrics_df['Portfolio'] == 'Group_5_ReverseLowGM']  # 最高毛利率

        analysis = {}

        if not low_margin_group.empty and not high_margin_group.empty:
            low_return = low_margin_group['Annualized_Return'].iloc[0]
            high_return = high_margin_group['Annualized_Return'].iloc[0]

            analysis['reverse_factor_effectiveness'] = {
                'low_margin_return': low_return,
                'high_margin_return': high_return,
                'return_spread': low_return - high_return,
                'reverse_factor_works': low_return > high_return
            }

        # 计算因子单调性（期望递减）
        returns = metrics_df['Annualized_Return'].values
        monotonicity = self.calculate_reverse_monotonicity(returns)
        analysis['reverse_monotonicity'] = monotonicity

        # IC分析（信息系数）
        ic_analysis = self.calculate_reverse_information_coefficient()
        analysis['reverse_ic_analysis'] = ic_analysis

        self.backtest_results['reverse_factor_analysis'] = analysis
        return analysis

    def calculate_reverse_monotonicity(self, returns: np.ndarray) -> float:
        """计算反向因子单调性（期望递减）"""
        if len(returns) < 2:
            return 0.0

        # 计算相邻组合收益率差异的符号一致性
        diffs = np.diff(returns)
        if len(diffs) == 0:
            return 0.0

        # 单调性 = 符号一致的比例
        signs = np.sign(diffs)
        if len(signs) == 0:
            return 0.0

        # 期望是递减的（低毛利率组合收益更高）
        expected_sign = -1
        monotonicity = np.sum(signs == expected_sign) / len(signs)

        return monotonicity

    def calculate_reverse_information_coefficient(self) -> Dict:
        """计算反向信息系数"""
        try:
            if 'portfolio_returns' not in self.backtest_results:
                return {}

            portfolio_returns = self.backtest_results['portfolio_returns']
            portfolio_holdings = self.backtest_results['portfolio_holdings']

            ic_values = []

            # 对每个调仓期计算IC（反向）
            for group_name in portfolio_returns:
                returns_df = portfolio_returns[group_name]
                holdings = portfolio_holdings[group_name]

                for i, (_, row) in enumerate(returns_df.iterrows()):
                    if i < len(holdings):
                        period_return = row['return']
                        avg_gross_margin = holdings[i]['avg_gross_margin']
                        # 反向关系：毛利率越低，期望收益越高
                        ic_values.append({'gross_margin': -avg_gross_margin, 'return': period_return})

            if not ic_values:
                return {}

            ic_df = pd.DataFrame(ic_values)

            # 计算相关系数
            correlation = ic_df['gross_margin'].corr(ic_df['return'])

            return {
                'reverse_ic_mean': correlation,
                'reverse_ic_std': ic_df['gross_margin'].std(),
                'reverse_ic_ir': correlation / ic_df['gross_margin'].std() if ic_df['gross_margin'].std() > 0 else 0,
                'samples': len(ic_values)
            }

        except Exception as e:
            print(f"⚠️  计算反向IC失败: {e}")
            return {}

    def create_reverse_backtest_visualization(self, output_file: str = "reverse_gross_margin_factor_backtest_results.html"):
        """创建反向回测结果可视化"""
        print(f"\n📊 创建反向毛利率因子回测结果可视化...")

        if 'portfolio_returns' not in self.backtest_results:
            print("❌ 请先运行回测模拟")
            return None

        portfolio_returns = self.backtest_results['portfolio_returns']

        # 创建子图
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('Reverse Gross Margin Factor - Cumulative Returns',
                          'Annualized Returns Comparison',
                          'Risk-Return Scatter',
                          'Gross Margin Distribution'),
            specs=[[{"secondary_y": False}, {"type": "bar"}],
                   [{"type": "scatter"}, {"type": "histogram"}]]
        )

        colors = ['#2E8B57', '#FF6347', '#4169E1', '#FFD700', '#8A2BE2']  # 不同的颜色方案

        # 1. 累积收益曲线
        for i, (group_name, returns_df) in enumerate(portfolio_returns.items()):
            if returns_df.empty:
                continue

            cumulative_returns = np.cumprod(1 + returns_df['return'].values)

            fig.add_trace(
                go.Scatter(
                    x=returns_df['date'],
                    y=cumulative_returns,
                    mode='lines',
                    name=group_name,
                    line=dict(color=colors[i % len(colors)], width=2),
                    showlegend=True
                ),
                row=1, col=1
            )

        # 2. 年化收益率对比
        if 'performance_metrics' in self.backtest_results:
            metrics_df = self.backtest_results['performance_metrics']

            fig.add_trace(
                go.Bar(
                    x=metrics_df['Portfolio'],
                    y=metrics_df['Annualized_Return'] * 100,
                    name='Annualized Return (%)',
                    marker_color=colors[:len(metrics_df)],
                    showlegend=False
                ),
                row=1, col=2
            )

            # 3. 风险收益散点图
            fig.add_trace(
                go.Scatter(
                    x=metrics_df['Volatility'] * 100,
                    y=metrics_df['Annualized_Return'] * 100,
                    mode='markers+text',
                    text=metrics_df['Portfolio'],
                    textposition='top center',
                    marker=dict(size=12, color=colors[:len(metrics_df)]),
                    name='Risk-Return',
                    showlegend=False
                ),
                row=2, col=1
            )

        # 4. 毛利率分布
        if self.gross_margin_data is not None:
            fig.add_trace(
                go.Histogram(
                    x=self.gross_margin_data['gross_margin'] * 100,
                    nbinsx=30,
                    name='Gross Margin Distribution',
                    marker_color='lightcoral',
                    showlegend=False
                ),
                row=2, col=2
            )

        # 更新布局
        fig.update_layout(
            title_text="HSI Reverse Gross Margin Factor Backtest Results",
            showlegend=True,
            height=800,
            width=1200
        )

        # 保存图表
        fig.write_html(output_file)
        print(f"📊 反向因子可视化结果已保存到: {output_file}")

        return fig

    def generate_reverse_backtest_report(self) -> str:
        """生成反向毛利率因子回测报告"""
        if 'performance_metrics' not in self.backtest_results:
            return "❌ 请先运行回测分析"

        metrics_df = self.backtest_results['performance_metrics']
        factor_analysis = self.backtest_results.get('reverse_factor_analysis', {})

        report = []
        report.append("🎯 恒生指数反向毛利率因子回测报告")
        report.append("=" * 50)
        report.append("")
        report.append("📝 策略说明: 低毛利率作为正向信号，高毛利率作为负向信号")
        report.append("")

        # 绩效指标
        report.append("📈 各组合绩效指标:")
        report.append("-" * 30)
        for _, row in metrics_df.iterrows():
            report.append(f"{row['Portfolio']}:")
            report.append(f"  年化收益率: {row['Annualized_Return']:.2%}")
            report.append(f"  波动率: {row['Volatility']:.2%}")
            report.append(f"  夏普比率: {row['Sharpe_Ratio']:.3f}")
            report.append(f"  最大回撤: {row['Max_Drawdown']:.2%}")
            report.append(f"  胜率: {row['Win_Rate']:.2%}")
            report.append("")

        # 反向因子有效性分析
        if 'reverse_factor_effectiveness' in factor_analysis:
            effectiveness = factor_analysis['reverse_factor_effectiveness']
            report.append("🔍 反向因子有效性分析:")
            report.append("-" * 30)
            report.append(f"低毛利率组合年化收益: {effectiveness['low_margin_return']:.2%}")
            report.append(f"高毛利率组合年化收益: {effectiveness['high_margin_return']:.2%}")
            report.append(f"收益差: {effectiveness['return_spread']:.2%}")
            report.append(f"反向因子是否有效: {'✅ 是' if effectiveness['reverse_factor_works'] else '❌ 否'}")
            report.append("")

        # 单调性分析
        if 'reverse_monotonicity' in factor_analysis:
            report.append(f"📊 反向因子单调性: {factor_analysis['reverse_monotonicity']:.2%}")
            report.append("")

        # IC分析
        if 'reverse_ic_analysis' in factor_analysis:
            ic = factor_analysis['reverse_ic_analysis']
            report.append("📊 反向信息系数(IC)分析:")
            report.append("-" * 30)
            report.append(f"反向IC均值: {ic.get('reverse_ic_mean', 0):.4f}")
            report.append(f"反向IC标准差: {ic.get('reverse_ic_std', 0):.4f}")
            report.append(f"反向IC信息比率: {ic.get('reverse_ic_ir', 0):.4f}")
            report.append(f"样本数: {ic.get('samples', 0)}")
            report.append("")

        # 策略建议
        report.append("💡 投资策略建议:")
        report.append("-" * 30)
        if 'reverse_factor_effectiveness' in factor_analysis:
            effectiveness = factor_analysis['reverse_factor_effectiveness']
            if effectiveness['reverse_factor_works']:
                report.append("✅ 反向毛利率因子在恒生指数中有效")
                report.append("📈 建议重点关注低毛利率但基本面稳健的股票")
                report.append("🎯 可以构建低毛利率股票组合获取超额收益")
            else:
                report.append("❌ 反向毛利率因子效果不明显")
                report.append("⚠️  建议结合其他因子进行多因子策略")

        return "\n".join(report)

def main():
    """主函数"""
    start_time = time.time()

    print("🚀 恒生指数反向毛利率因子回测分析")
    print("=" * 50)

    # 创建回测器
    backtest = HSIReverseGrossMarginFactorBacktest()

    # 1. 加载毛利率数据
    if not backtest.load_gross_margin_data():
        print("❌ 无法加载毛利率数据，程序退出")
        return

    # 2. 加载价格数据（从ROE回测缓存）
    if not backtest.load_all_price_data():
        print("❌ 价格数据加载失败，程序退出")
        return

    # 3. 准备回测数据
    backtest_data, rebalance_dates = backtest.prepare_backtest_data(
        start_date="2021-01-01",
        end_date="2024-12-31"
    )

    # 4. 运行反向回测模拟
    portfolio_returns = backtest.simulate_reverse_portfolio_returns(backtest_data, rebalance_dates, n_groups=5)

    # 5. 计算绩效指标
    metrics_df = backtest.calculate_performance_metrics()

    # 6. 分析反向因子有效性
    factor_analysis = backtest.analyze_reverse_factor_effectiveness()

    # 7. 生成报告
    report = backtest.generate_reverse_backtest_report()
    print("\n" + report)

    # 8. 创建可视化
    backtest.create_reverse_backtest_visualization()

    # 9. 保存结果
    output_dir = "backtest_results"
    os.makedirs(output_dir, exist_ok=True)

    # 保存绩效指标
    metrics_df.to_csv(f"{output_dir}/reverse_gross_margin_factor_performance_metrics.csv", index=False)

    # 保存报告
    with open(f"{output_dir}/reverse_gross_margin_factor_backtest_report.txt", 'w', encoding='utf-8') as f:
        f.write(report)

    elapsed_time = time.time() - start_time
    print(f"\n✅ 反向毛利率因子回测分析完成，总耗时: {elapsed_time:.2f} 秒")
    print(f"📁 结果文件保存在: {output_dir}/")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
恒生指数毛利率因子综合分析报告

功能：
1. 对比正向和反向毛利率因子回测结果
2. 分析毛利率因子在港股市场的特征
3. 提供投资策略建议
4. 生成综合可视化报告

作者: AI Assistant
创建时间: 2025年1月
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import plotly.offline as pyo

def load_backtest_results():
    """加载回测结果数据"""
    try:
        # 加载正向毛利率因子结果
        forward_metrics = pd.read_csv("backtest_results/gross_margin_factor_performance_metrics.csv")
        forward_metrics['Strategy'] = 'Forward_GM'
        
        # 加载反向毛利率因子结果
        reverse_metrics = pd.read_csv("backtest_results/reverse_gross_margin_factor_performance_metrics.csv")
        reverse_metrics['Strategy'] = 'Reverse_GM'
        
        return forward_metrics, reverse_metrics
    except Exception as e:
        print(f"❌ 加载回测结果失败: {e}")
        return None, None

def analyze_factor_effectiveness(forward_metrics, reverse_metrics):
    """分析因子有效性"""
    analysis = {}
    
    # 正向毛利率因子分析
    if not forward_metrics.empty:
        high_gm = forward_metrics[forward_metrics['Portfolio'] == 'Group_1_GrossMargin']
        low_gm = forward_metrics[forward_metrics['Portfolio'] == 'Group_5_GrossMargin']
        
        if not high_gm.empty and not low_gm.empty:
            analysis['forward'] = {
                'high_gm_return': high_gm['Annualized_Return'].iloc[0],
                'low_gm_return': low_gm['Annualized_Return'].iloc[0],
                'spread': high_gm['Annualized_Return'].iloc[0] - low_gm['Annualized_Return'].iloc[0],
                'effective': high_gm['Annualized_Return'].iloc[0] > low_gm['Annualized_Return'].iloc[0]
            }
    
    # 反向毛利率因子分析
    if not reverse_metrics.empty:
        low_gm_rev = reverse_metrics[reverse_metrics['Portfolio'] == 'Group_1_ReverseLowGM']
        high_gm_rev = reverse_metrics[reverse_metrics['Portfolio'] == 'Group_5_ReverseLowGM']
        
        if not low_gm_rev.empty and not high_gm_rev.empty:
            analysis['reverse'] = {
                'low_gm_return': low_gm_rev['Annualized_Return'].iloc[0],
                'high_gm_return': high_gm_rev['Annualized_Return'].iloc[0],
                'spread': low_gm_rev['Annualized_Return'].iloc[0] - high_gm_rev['Annualized_Return'].iloc[0],
                'effective': low_gm_rev['Annualized_Return'].iloc[0] > high_gm_rev['Annualized_Return'].iloc[0]
            }
    
    return analysis

def find_best_performing_groups(forward_metrics, reverse_metrics):
    """找出表现最好的组合"""
    all_metrics = pd.concat([forward_metrics, reverse_metrics], ignore_index=True)
    
    # 按年化收益率排序
    best_performers = all_metrics.nlargest(3, 'Annualized_Return')
    
    return best_performers

def create_comprehensive_visualization(forward_metrics, reverse_metrics):
    """创建综合对比可视化"""
    
    # 创建子图
    fig = make_subplots(
        rows=2, cols=2,
        subplot_titles=('Annual Returns Comparison', 
                       'Risk-Return Analysis', 
                       'Sharpe Ratio Comparison',
                       'Max Drawdown Comparison'),
        specs=[[{"type": "bar"}, {"type": "scatter"}],
               [{"type": "bar"}, {"type": "bar"}]]
    )
    
    # 合并数据
    all_metrics = pd.concat([forward_metrics, reverse_metrics], ignore_index=True)
    
    # 1. 年化收益率对比
    colors_forward = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd']
    colors_reverse = ['#2E8B57', '#FF6347', '#4169E1', '#FFD700', '#8A2BE2']
    
    forward_data = forward_metrics.sort_values('Portfolio')
    reverse_data = reverse_metrics.sort_values('Portfolio')
    
    fig.add_trace(
        go.Bar(
            x=forward_data['Portfolio'],
            y=forward_data['Annualized_Return'] * 100,
            name='Forward GM Factor',
            marker_color=colors_forward,
            showlegend=True
        ),
        row=1, col=1
    )
    
    fig.add_trace(
        go.Bar(
            x=reverse_data['Portfolio'],
            y=reverse_data['Annualized_Return'] * 100,
            name='Reverse GM Factor',
            marker_color=colors_reverse,
            showlegend=True
        ),
        row=1, col=1
    )
    
    # 2. 风险收益散点图
    fig.add_trace(
        go.Scatter(
            x=forward_metrics['Volatility'] * 100,
            y=forward_metrics['Annualized_Return'] * 100,
            mode='markers',
            name='Forward GM',
            marker=dict(size=10, color=colors_forward),
            text=forward_metrics['Portfolio'],
            showlegend=True
        ),
        row=1, col=2
    )
    
    fig.add_trace(
        go.Scatter(
            x=reverse_metrics['Volatility'] * 100,
            y=reverse_metrics['Annualized_Return'] * 100,
            mode='markers',
            name='Reverse GM',
            marker=dict(size=10, color=colors_reverse, symbol='diamond'),
            text=reverse_metrics['Portfolio'],
            showlegend=True
        ),
        row=1, col=2
    )
    
    # 3. 夏普比率对比
    fig.add_trace(
        go.Bar(
            x=forward_data['Portfolio'],
            y=forward_data['Sharpe_Ratio'],
            name='Forward Sharpe',
            marker_color=colors_forward,
            showlegend=False
        ),
        row=2, col=1
    )
    
    fig.add_trace(
        go.Bar(
            x=reverse_data['Portfolio'],
            y=reverse_data['Sharpe_Ratio'],
            name='Reverse Sharpe',
            marker_color=colors_reverse,
            showlegend=False
        ),
        row=2, col=1
    )
    
    # 4. 最大回撤对比
    fig.add_trace(
        go.Bar(
            x=forward_data['Portfolio'],
            y=forward_data['Max_Drawdown'] * 100,
            name='Forward Drawdown',
            marker_color=colors_forward,
            showlegend=False
        ),
        row=2, col=2
    )
    
    fig.add_trace(
        go.Bar(
            x=reverse_data['Portfolio'],
            y=reverse_data['Max_Drawdown'] * 100,
            name='Reverse Drawdown',
            marker_color=colors_reverse,
            showlegend=False
        ),
        row=2, col=2
    )
    
    # 更新布局
    fig.update_layout(
        title_text="HSI Gross Margin Factor Comprehensive Analysis",
        showlegend=True,
        height=800,
        width=1400
    )
    
    # 更新坐标轴标签
    fig.update_xaxes(title_text="Portfolio Groups", row=1, col=1)
    fig.update_yaxes(title_text="Annual Return (%)", row=1, col=1)
    
    fig.update_xaxes(title_text="Volatility (%)", row=1, col=2)
    fig.update_yaxes(title_text="Annual Return (%)", row=1, col=2)
    
    fig.update_xaxes(title_text="Portfolio Groups", row=2, col=1)
    fig.update_yaxes(title_text="Sharpe Ratio", row=2, col=1)
    
    fig.update_xaxes(title_text="Portfolio Groups", row=2, col=2)
    fig.update_yaxes(title_text="Max Drawdown (%)", row=2, col=2)
    
    # 保存图表
    fig.write_html("gross_margin_factor_comprehensive_analysis.html")
    print("📊 综合分析可视化已保存到: gross_margin_factor_comprehensive_analysis.html")
    
    return fig

def generate_comprehensive_report(forward_metrics, reverse_metrics, factor_analysis, best_performers):
    """生成综合分析报告"""
    
    report = []
    report.append("🎯 恒生指数毛利率因子综合分析报告")
    report.append("=" * 60)
    report.append("")
    
    # 执行摘要
    report.append("📋 执行摘要")
    report.append("-" * 30)
    report.append("本报告对恒生指数成分股的毛利率因子进行了全面回测分析，")
    report.append("包括正向毛利率因子（高毛利率为正向信号）和反向毛利率因子")
    report.append("（低毛利率为正向信号）两种策略的对比研究。")
    report.append("")
    
    # 关键发现
    report.append("🔍 关键发现")
    report.append("-" * 30)
    
    if 'forward' in factor_analysis:
        forward = factor_analysis['forward']
        report.append(f"1. 正向毛利率因子:")
        report.append(f"   - 高毛利率组合年化收益: {forward['high_gm_return']:.2%}")
        report.append(f"   - 低毛利率组合年化收益: {forward['low_gm_return']:.2%}")
        report.append(f"   - 收益差: {forward['spread']:.2%}")
        report.append(f"   - 因子有效性: {'✅ 有效' if forward['effective'] else '❌ 无效'}")
        report.append("")
    
    if 'reverse' in factor_analysis:
        reverse = factor_analysis['reverse']
        report.append(f"2. 反向毛利率因子:")
        report.append(f"   - 低毛利率组合年化收益: {reverse['low_gm_return']:.2%}")
        report.append(f"   - 高毛利率组合年化收益: {reverse['high_gm_return']:.2%}")
        report.append(f"   - 收益差: {reverse['spread']:.2%}")
        report.append(f"   - 因子有效性: {'✅ 有效' if reverse['effective'] else '❌ 无效'}")
        report.append("")
    
    # 最佳表现组合
    report.append("🏆 最佳表现组合")
    report.append("-" * 30)
    for i, (_, row) in enumerate(best_performers.iterrows()):
        report.append(f"{i+1}. {row['Portfolio']} ({row['Strategy']})")
        report.append(f"   - 年化收益率: {row['Annualized_Return']:.2%}")
        report.append(f"   - 夏普比率: {row['Sharpe_Ratio']:.3f}")
        report.append(f"   - 最大回撤: {row['Max_Drawdown']:.2%}")
        report.append("")
    
    # 市场特征分析
    report.append("📊 港股市场毛利率因子特征")
    report.append("-" * 30)
    report.append("1. 毛利率因子在恒生指数中表现出复杂的非线性特征")
    report.append("2. 传统的高毛利率策略在港股市场效果有限")
    report.append("3. 中等毛利率组合可能提供更好的风险调整收益")
    report.append("4. 因子效应可能受到行业结构和市场环境影响")
    report.append("")
    
    # 投资建议
    report.append("💡 投资策略建议")
    report.append("-" * 30)
    
    # 找出最佳策略
    best_group = best_performers.iloc[0]
    report.append(f"🎯 推荐策略: {best_group['Portfolio']} ({best_group['Strategy']})")
    report.append(f"   - 预期年化收益: {best_group['Annualized_Return']:.2%}")
    report.append(f"   - 风险水平: {best_group['Volatility']:.2%}")
    report.append(f"   - 风险调整收益: {best_group['Sharpe_Ratio']:.3f}")
    report.append("")
    
    report.append("📈 实施建议:")
    report.append("1. 采用季度调仓频率，与财务报告发布周期同步")
    report.append("2. 结合其他基本面因子（如ROE、营收增长）进行多因子策略")
    report.append("3. 注意控制组合集中度，避免行业过度集中")
    report.append("4. 定期监控因子有效性，适时调整策略参数")
    report.append("")
    
    # 风险提示
    report.append("⚠️  风险提示")
    report.append("-" * 30)
    report.append("1. 回测期间相对较短，需要更长期数据验证")
    report.append("2. 港股市场波动较大，需要做好风险管理")
    report.append("3. 因子效应可能随市场环境变化而衰减")
    report.append("4. 建议将毛利率因子作为多因子策略的一部分使用")
    
    return "\n".join(report)

def main():
    """主函数"""
    print("🚀 恒生指数毛利率因子综合分析")
    print("=" * 50)
    
    # 1. 加载回测结果
    forward_metrics, reverse_metrics = load_backtest_results()
    
    if forward_metrics is None or reverse_metrics is None:
        print("❌ 无法加载回测结果，请先运行毛利率因子回测")
        return
    
    print("✅ 回测结果加载成功")
    
    # 2. 分析因子有效性
    factor_analysis = analyze_factor_effectiveness(forward_metrics, reverse_metrics)
    
    # 3. 找出最佳表现组合
    best_performers = find_best_performing_groups(forward_metrics, reverse_metrics)
    
    # 4. 创建综合可视化
    create_comprehensive_visualization(forward_metrics, reverse_metrics)
    
    # 5. 生成综合报告
    comprehensive_report = generate_comprehensive_report(
        forward_metrics, reverse_metrics, factor_analysis, best_performers
    )
    
    print("\n" + comprehensive_report)
    
    # 6. 保存报告
    with open("backtest_results/gross_margin_factor_comprehensive_report.txt", 'w', encoding='utf-8') as f:
        f.write(comprehensive_report)
    
    print(f"\n✅ 综合分析完成")
    print(f"📁 报告已保存到: backtest_results/gross_margin_factor_comprehensive_report.txt")

if __name__ == "__main__":
    main()

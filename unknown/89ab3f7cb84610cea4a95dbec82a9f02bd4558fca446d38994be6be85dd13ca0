"""
恒生指数成分股季度营收数据获取器

该脚本用于获取恒生指数成分股的季度营收数据。

主要功能：
1. 从东方财富API获取港股财务数据（利润表）
2. 提取季度营收指标
3. 计算营收增长率（同比、环比）
4. 缓存数据以提高性能
5. 生成统计报告
"""

import os
import sys
import pandas as pd
import numpy as np
import requests
import json
import time
import pickle
from datetime import datetime, timedelta
from tqdm import tqdm
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
import warnings
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("hsi_revenue_download.log"),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class HSIQuarterlyRevenueDownloader:
    def __init__(self):
        """初始化下载器"""
        self.output_dir = "data/hsi_quarterly_revenue_data"
        self.cache_dir = "data/hsi_quarterly_revenue_cache"
        self.constituents_file = "data_files/hsi_constituents.csv"
        
        # 创建输出目录
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(self.cache_dir, exist_ok=True)
        
        self.hsi_constituents = []
        self.financial_data = {}
        
    def load_hsi_constituents(self) -> bool:
        """加载恒生指数成分股列表"""
        try:
            logger.info(f"📁 从 {self.constituents_file} 加载恒生指数成分股...")
            
            df = pd.read_csv(self.constituents_file, dtype={'代码': str})
            
            # 格式化股票代码
            hsi_stocks = []
            for _, row in df.iterrows():
                code = str(row['代码']).strip()
                name = str(row['名称']).strip()
                if code and code != 'nan':
                    code_formatted = str(code).zfill(5)  # 补齐到5位数
                    hsi_stocks.append({
                        'code': code_formatted,
                        'name': name
                    })
            
            self.hsi_constituents = hsi_stocks
            logger.info(f"✅ 已加载 {len(hsi_stocks)} 只恒生指数成分股")
            return True
            
        except Exception as e:
            logger.error(f"❌ 加载成分股列表失败: {e}")
            return False
    
    def format_hk_stock_code(self, code):
        """格式化港股代码，用于API请求"""
        # 确保代码是字符串并补齐前导零
        code_str = str(code).zfill(5)
        # 对于港股，使用5位数字格式加.HK后缀
        return f"{code_str}.HK"
    
    def get_cache_file_path(self, stock_code, data_type="financial"):
        """获取缓存文件路径"""
        return os.path.join(self.cache_dir, f"{stock_code}_{data_type}_data.pkl")
    
    def is_cache_valid(self, stock_code, max_age_days=1):
        """检查缓存是否有效（1天内的数据认为有效，因为需要获取10年历史数据）"""
        cache_file = self.get_cache_file_path(stock_code)
        if not os.path.exists(cache_file):
            return False
        
        # 检查文件修改时间
        file_time = datetime.fromtimestamp(os.path.getmtime(cache_file))
        age = datetime.now() - file_time
        return age.days < max_age_days
    
    def load_cached_data(self, stock_code):
        """从缓存加载数据"""
        cache_file = self.get_cache_file_path(stock_code)
        try:
            with open(cache_file, 'rb') as f:
                return pickle.load(f)
        except Exception as e:
            logger.warning(f"⚠️  加载 {stock_code} 缓存失败: {e}")
            return None
    
    def save_to_cache(self, stock_code, data):
        """保存数据到缓存"""
        cache_file = self.get_cache_file_path(stock_code)
        try:
            with open(cache_file, 'wb') as f:
                pickle.dump(data, f)
        except Exception as e:
            logger.warning(f"⚠️  保存 {stock_code} 缓存失败: {e}")

    def filter_recent_years(self, df, years=10):
        """过滤最近N年的数据"""
        try:
            if df.empty or 'REPORT_DATE' not in df.columns:
                return df

            # 转换日期列
            df['REPORT_DATE'] = pd.to_datetime(df['REPORT_DATE'])

            # 计算截止日期（从当前日期往前推N年）
            cutoff_date = datetime.now() - timedelta(days=years * 365)

            # 过滤数据
            filtered_df = df[df['REPORT_DATE'] >= cutoff_date].copy()

            return filtered_df

        except Exception as e:
            logger.warning(f"⚠️  过滤数据时出错: {e}")
            return df

    def download_financial_data(self, stock_code, stock_name):
        """下载单只股票的财务数据（利润表）"""
        try:
            # 格式化股票代码
            formatted_code = self.format_hk_stock_code(stock_code)
            logger.info(f"📊 下载 {formatted_code} ({stock_name}) 的财务数据")

            # 构建API URL - 使用港股利润表接口，增加页面大小以获取更多历史数据
            url = f"https://datacenter.eastmoney.com/securities/api/data/v1/get?reportName=RPT_HKF10_FN_INCOME_PC&columns=ALL&quoteColumns=&filter=(SECUCODE%3D%22{formatted_code}%22)&pageNumber=1&pageSize=200&sortTypes=-1&sortColumns=REPORT_DATE&source=F10&client=PC"

            # 发送请求
            response = requests.get(url, timeout=30)

            if response.status_code != 200:
                logger.warning(f"⚠️  请求失败: {formatted_code}, 状态码: {response.status_code}")
                return None

            # 解析JSON响应
            data = response.json()

            if 'result' not in data or 'data' not in data['result'] or not data['result']['data']:
                logger.warning(f"⚠️  未找到 {formatted_code} 的财务数据")
                return None

            # 提取财务数据
            financial_data = data['result']['data']
            logger.info(f"✅ 获取到 {formatted_code} 的 {len(financial_data)} 条财务记录")

            # 转换为DataFrame
            df = pd.DataFrame(financial_data)

            # 过滤最近10年的数据
            df = self.filter_recent_years(df, years=10)
            logger.info(f"📅 过滤后保留最近10年的 {len(df)} 条记录")

            # 处理数据：提取营收
            processed_df = self.process_income_statement_data(df, stock_code, stock_name)

            if processed_df is not None:
                # 保存到缓存
                self.save_to_cache(stock_code, processed_df)

                # 保存原始数据到文件
                output_file = os.path.join(self.output_dir, f"{stock_code}_quarterly_revenue.csv")
                processed_df.to_csv(output_file, index=False, encoding='utf-8-sig')
                logger.info(f"💾 已保存 {formatted_code} 的季度营收数据到 {output_file}")
                return processed_df
            else:
                logger.warning(f"⚠️  处理 {formatted_code} 的财务数据失败")
                return None

        except Exception as e:
            logger.error(f"❌ 下载 {stock_code} 的财务数据时出错: {str(e)}")
            return None

    def process_income_statement_data(self, df, stock_code, stock_name):
        """处理利润表数据，提取营收"""
        try:
            # 确保必要的列存在
            required_columns = ['REPORT_DATE', 'STD_ITEM_CODE', 'STD_ITEM_NAME', 'AMOUNT']
            for col in required_columns:
                if col not in df.columns:
                    logger.warning(f"⚠️  {stock_code} 的数据缺少必要的列: {col}")
                    return None

            # 转换日期列
            df['REPORT_DATE'] = pd.to_datetime(df['REPORT_DATE'])

            # 获取所有唯一的报告日期
            report_dates = sorted(df['REPORT_DATE'].unique())
            result_rows = []

            # 对每个报告日期处理数据
            for date in report_dates:
                row = {
                    'stock_code': stock_code,
                    'stock_name': stock_name,
                    'report_date': date
                }

                # 提取该日期的数据
                date_df = df[df['REPORT_DATE'] == date]

                # 查找收入数据 - 港股特有字段名称
                revenue_items = date_df[date_df['STD_ITEM_NAME'].str.contains('营业额|营运收入|经营收入总额|营业收入|收入总额', na=False)]

                # 优先级排序：营业额 > 营运收入 > 经营收入总额 > 营业收入 > 收入总额
                revenue_priority = ['营业额', '营运收入', '经营收入总额', '营业收入', '收入总额']

                for priority_name in revenue_priority:
                    priority_items = revenue_items[revenue_items['STD_ITEM_NAME'].str.contains(priority_name, na=False)]
                    if not priority_items.empty:
                        # 排除"其他"项目
                        non_other_items = priority_items[~priority_items['STD_ITEM_NAME'].str.contains('其他', na=False)]
                        if not non_other_items.empty:
                            row['revenue'] = non_other_items.iloc[0]['AMOUNT']
                            row['revenue_item_name'] = non_other_items.iloc[0]['STD_ITEM_NAME']
                            break
                        else:
                            row['revenue'] = priority_items.iloc[0]['AMOUNT']
                            row['revenue_item_name'] = priority_items.iloc[0]['STD_ITEM_NAME']
                            break

                # 只有当找到了收入数据时才添加这一行
                if 'revenue' in row:
                    try:
                        # 确保营收是数值类型
                        row['revenue'] = float(row['revenue']) if row['revenue'] else 0
                        result_rows.append(row)
                    except (ValueError, TypeError):
                        logger.warning(f"⚠️  {stock_code} 在 {date} 的营收数据无法转换为数值")

            # 创建结果DataFrame
            if not result_rows:
                logger.warning(f"⚠️  {stock_code} 没有找到收入数据")
                return None

            result_df = pd.DataFrame(result_rows)

            # 添加年份和季度信息
            result_df['year'] = result_df['report_date'].dt.year
            result_df['month'] = result_df['report_date'].dt.month
            result_df['quarter'] = result_df['month'].apply(lambda x: (x-1)//3 + 1)
            result_df['year_quarter'] = result_df['year'].astype(str) + '-Q' + result_df['quarter'].astype(str)

            # 确保数值列是数值类型
            result_df['revenue'] = pd.to_numeric(result_df['revenue'], errors='coerce')

            # 按日期排序
            result_df = result_df.sort_values('report_date').reset_index(drop=True)

            # 计算增长率
            result_df = self.calculate_growth_rates(result_df)

            return result_df

        except Exception as e:
            logger.error(f"❌ 处理 {stock_code} 的财务数据时出错: {str(e)}")
            return None

    def calculate_growth_rates(self, df):
        """计算营收增长率（同比、环比）"""
        try:
            # 确保数据按日期排序
            df = df.sort_values('report_date').reset_index(drop=True)

            # 初始化增长率列
            df['revenue_yoy'] = np.nan  # 同比增长率
            df['revenue_qoq'] = np.nan  # 环比增长率

            # 计算同比增长率（Year-over-Year）
            for quarter in range(1, 5):
                quarter_data = df[df['quarter'] == quarter].copy()
                quarter_data = quarter_data.sort_values('year').reset_index(drop=True)

                for i in range(1, len(quarter_data)):
                    current_year = quarter_data.iloc[i]['year']
                    prev_year = quarter_data.iloc[i-1]['year']

                    # 确保是相邻年份
                    if current_year == prev_year + 1:
                        current_revenue = quarter_data.iloc[i]['revenue']
                        prev_revenue = quarter_data.iloc[i-1]['revenue']

                        if prev_revenue and prev_revenue != 0:
                            yoy_growth = (current_revenue - prev_revenue) / prev_revenue * 100
                            # 找到原始DataFrame中的索引
                            original_idx = df[(df['year'] == current_year) & (df['quarter'] == quarter)].index[0]
                            df.loc[original_idx, 'revenue_yoy'] = yoy_growth

            # 计算环比增长率（Quarter-over-Quarter）
            for i in range(1, len(df)):
                current_revenue = df.iloc[i]['revenue']
                prev_revenue = df.iloc[i-1]['revenue']

                if prev_revenue and prev_revenue != 0:
                    qoq_growth = (current_revenue - prev_revenue) / prev_revenue * 100
                    df.iloc[i, df.columns.get_loc('revenue_qoq')] = qoq_growth

            return df

        except Exception as e:
            logger.error(f"❌ 计算增长率时出错: {str(e)}")
            return df

    def process_quarterly_data(self, quarterly_data, stock_code):
        """处理季度数据，添加统计信息"""
        try:
            if quarterly_data is None or quarterly_data.empty:
                logger.warning(f"⚠️  {stock_code} 没有季度数据")
                return None

            # 确保数据按日期排序
            quarterly_data = quarterly_data.sort_values('report_date').reset_index(drop=True)

            # 添加数据来源标记
            quarterly_data['data_source'] = 'quarterly_report'
            quarterly_data['last_update'] = datetime.now()

            # 计算营收变化趋势
            if 'revenue' in quarterly_data.columns and len(quarterly_data) > 1:
                quarterly_data['revenue_change'] = quarterly_data['revenue'].diff()
                quarterly_data['revenue_pct_change'] = quarterly_data['revenue'].pct_change()

            return quarterly_data

        except Exception as e:
            logger.error(f"❌ 处理 {stock_code} 季度数据时出错: {str(e)}")
            return None

    def download_all_stocks(self, max_workers=3, use_cache=True):
        """下载所有恒生指数成分股的营收数据"""
        if not self.hsi_constituents:
            logger.error("❌ 请先加载恒生指数成分股列表")
            return False

        logger.info(f"🚀 开始下载 {len(self.hsi_constituents)} 只恒生指数成分股的营收数据")

        success_count = 0
        cached_count = 0
        failed_stocks = []

        # 使用ThreadPoolExecutor进行并行处理
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 创建任务
            future_to_stock = {}

            for stock in self.hsi_constituents:
                stock_code = stock['code']
                stock_name = stock['name']

                # 检查缓存
                if use_cache and self.is_cache_valid(stock_code):
                    cached_data = self.load_cached_data(stock_code)
                    if cached_data is not None:
                        self.financial_data[stock_code] = cached_data
                        success_count += 1
                        cached_count += 1
                        logger.info(f"📁 {stock_code} ({stock_name}) 使用缓存数据")
                        continue

                # 提交下载任务
                future = executor.submit(self.download_financial_data, stock_code, stock_name)
                future_to_stock[future] = (stock_code, stock_name)

            # 处理完成的任务
            for future in tqdm(as_completed(future_to_stock),
                             total=len(future_to_stock),
                             desc="下载财务数据"):
                stock_code, stock_name = future_to_stock[future]

                try:
                    result = future.result()
                    if result is not None:
                        self.financial_data[stock_code] = result
                        success_count += 1
                    else:
                        failed_stocks.append((stock_code, stock_name))
                except Exception as e:
                    logger.error(f"❌ 处理 {stock_code} ({stock_name}) 时出错: {str(e)}")
                    failed_stocks.append((stock_code, stock_name))

                # 添加延迟避免API限制
                time.sleep(0.5)

        # 报告结果
        logger.info(f"📊 下载完成统计:")
        logger.info(f"  ✅ 成功: {success_count}/{len(self.hsi_constituents)}")
        logger.info(f"  📁 缓存: {cached_count}")
        logger.info(f"  ❌ 失败: {len(failed_stocks)}")

        if failed_stocks:
            logger.warning("❌ 下载失败的股票:")
            for code, name in failed_stocks:
                logger.warning(f"  - {code} ({name})")

        return success_count > 0

    def generate_quarterly_summary(self):
        """生成所有股票的季度营收汇总数据"""
        if not self.financial_data:
            logger.error("❌ 没有财务数据可供处理，请先下载数据")
            return None

        logger.info("📈 开始生成季度营收汇总数据...")

        all_quarterly_data = []

        for stock_code, quarterly_data in self.financial_data.items():
            stock_name = quarterly_data['stock_name'].iloc[0] if not quarterly_data.empty else ''
            logger.info(f"📊 处理 {stock_code} ({stock_name}) 的季度数据...")

            # 处理季度数据
            processed_data = self.process_quarterly_data(quarterly_data, stock_code)

            if processed_data is not None:
                all_quarterly_data.append(processed_data)

                # 保存单个股票的季度数据
                output_file = os.path.join(self.output_dir, f"{stock_code}_quarterly_revenue_final.csv")
                processed_data.to_csv(output_file, index=False, encoding='utf-8-sig')
                logger.info(f"💾 已保存 {stock_code} 的季度营收数据")

        if all_quarterly_data:
            # 合并所有数据
            combined_data = pd.concat(all_quarterly_data, ignore_index=True)

            # 保存合并数据
            combined_file = os.path.join(self.output_dir, "hsi_all_quarterly_revenue.csv")
            combined_data.to_csv(combined_file, index=False, encoding='utf-8-sig')
            logger.info(f"💾 已保存所有股票的季度营收数据到 {combined_file}")

            # 生成数据统计报告
            self.generate_summary_report(combined_data)

            return combined_data
        else:
            logger.error("❌ 没有生成任何季度数据")
            return None

    def generate_summary_report(self, quarterly_data):
        """生成季度数据统计报告"""
        try:
            logger.info("📋 生成季度数据统计报告...")

            # 基本统计
            total_records = len(quarterly_data)
            unique_stocks = quarterly_data['stock_code'].nunique()
            date_range = f"{quarterly_data['report_date'].min().strftime('%Y-%m-%d')} 到 {quarterly_data['report_date'].max().strftime('%Y-%m-%d')}"

            # 营收统计
            valid_revenue_data = quarterly_data.dropna(subset=['revenue'])
            avg_revenue = valid_revenue_data['revenue'].mean() if not valid_revenue_data.empty else 0

            # 增长率统计
            valid_yoy_data = quarterly_data.dropna(subset=['revenue_yoy'])
            avg_yoy_growth = valid_yoy_data['revenue_yoy'].mean() if not valid_yoy_data.empty else 0

            valid_qoq_data = quarterly_data.dropna(subset=['revenue_qoq'])
            avg_qoq_growth = valid_qoq_data['revenue_qoq'].mean() if not valid_qoq_data.empty else 0

            # 按股票统计
            stock_stats = quarterly_data.groupby(['stock_code', 'stock_name']).agg({
                'revenue': ['count', 'mean', 'std', 'min', 'max'],
                'revenue_yoy': ['mean', 'std'],
                'revenue_qoq': ['mean', 'std']
            }).round(4)

            # 按年度统计
            quarterly_data['year'] = pd.to_datetime(quarterly_data['report_date']).dt.year
            yearly_stats = quarterly_data.groupby('year').agg({
                'revenue': ['count', 'mean'],
                'revenue_yoy': 'mean',
                'revenue_qoq': 'mean'
            }).round(4)

            # 保存统计报告
            report_file = os.path.join(self.output_dir, "quarterly_revenue_summary_report.txt")
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("恒生指数成分股季度营收数据统计报告\n")
                f.write("=" * 50 + "\n\n")
                f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"数据日期范围: {date_range}\n")
                f.write(f"总记录数: {total_records:,}\n")
                f.write(f"股票数量: {unique_stocks}\n")
                f.write(f"平均营收: {avg_revenue:,.0f}\n")
                f.write(f"平均同比增长率: {avg_yoy_growth:.2f}%\n")
                f.write(f"平均环比增长率: {avg_qoq_growth:.2f}%\n\n")

                f.write("各股票营收统计:\n")
                f.write("-" * 30 + "\n")
                for (code, name), stats in stock_stats.iterrows():
                    f.write(f"{code} ({name}):\n")
                    f.write(f"  季度数据点数: {int(stats[('revenue', 'count')])}\n")
                    f.write(f"  平均营收: {stats[('revenue', 'mean')]:,.0f}\n")
                    f.write(f"  营收标准差: {stats[('revenue', 'std')]:,.0f}\n")
                    f.write(f"  最小营收: {stats[('revenue', 'min')]:,.0f}\n")
                    f.write(f"  最大营收: {stats[('revenue', 'max')]:,.0f}\n")

                    # 安全处理可能的NaN值
                    yoy_mean = stats[('revenue_yoy', 'mean')]
                    qoq_mean = stats[('revenue_qoq', 'mean')]

                    if pd.notna(yoy_mean):
                        f.write(f"  平均同比增长率: {yoy_mean:.2f}%\n")
                    else:
                        f.write(f"  平均同比增长率: N/A\n")

                    if pd.notna(qoq_mean):
                        f.write(f"  平均环比增长率: {qoq_mean:.2f}%\n\n")
                    else:
                        f.write(f"  平均环比增长率: N/A\n\n")

                f.write("按年度统计:\n")
                f.write("-" * 20 + "\n")
                for year, stats in yearly_stats.iterrows():
                    f.write(f"{year}年: 数据点数={int(stats[('revenue', 'count')])}, ")
                    f.write(f"平均营收={stats[('revenue', 'mean')]:,.0f}, ")
                    f.write(f"平均同比增长率={stats['revenue_yoy']:.2f}%\n")

            logger.info(f"📋 统计报告已保存到 {report_file}")

            # 打印简要统计
            logger.info("📊 季度数据统计摘要:")
            logger.info(f"  📅 日期范围: {date_range}")
            logger.info(f"  📈 总记录数: {total_records:,}")
            logger.info(f"  🏢 股票数量: {unique_stocks}")
            logger.info(f"  💰 平均营收: {avg_revenue:,.0f}")
            logger.info(f"  📊 平均同比增长率: {avg_yoy_growth:.2f}%")
            logger.info(f"  📊 平均环比增长率: {avg_qoq_growth:.2f}%")

        except Exception as e:
            logger.error(f"❌ 生成统计报告时出错: {str(e)}")

def main():
    """主函数"""
    start_time = time.time()

    logger.info("🚀 恒生指数成分股季度营收数据获取器启动")
    logger.info("📝 注意：营收数据基于季度财务报表，获取最近10年历史数据")

    downloader = HSIQuarterlyRevenueDownloader()

    # 1. 加载成分股列表
    logger.info("📋 步骤1: 加载恒生指数成分股列表")
    if not downloader.load_hsi_constituents():
        logger.error("❌ 无法加载恒生指数成分股列表，程序退出")
        return

    # 2. 下载财务数据
    logger.info("📊 步骤2: 下载财务数据")
    if not downloader.download_all_stocks(max_workers=3, use_cache=True):
        logger.error("❌ 财务数据下载失败，程序退出")
        return

    # 3. 生成季度汇总数据
    logger.info("📈 步骤3: 生成季度营收汇总数据")
    quarterly_data = downloader.generate_quarterly_summary()

    if quarterly_data is not None:
        elapsed_time = time.time() - start_time
        logger.info(f"✅ 程序执行完成，总耗时: {elapsed_time:.2f} 秒")
        logger.info(f"📁 数据文件保存在: {downloader.output_dir}")
    else:
        logger.error("❌ 程序执行失败")

if __name__ == "__main__":
    main()

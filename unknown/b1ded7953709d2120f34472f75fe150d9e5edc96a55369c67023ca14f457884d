#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
恒生指数毛利率因子优化季度调仓回测

功能：
1. 优化的季度调仓时间（考虑财务报告发布时间）
2. 更合理的数据使用逻辑
3. 改进的因子构建方法
4. 详细的调仓时间分析

调仓时间安排：
- Q1财报调仓：5月初（Q1财报通常4月底发布）
- Q2财报调仓：8月初（Q2财报通常7月底发布）  
- Q3财报调仓：11月初（Q3财报通常10月底发布）
- Q4财报调仓：2月初（Q4财报通常1月底发布）

作者: AI Assistant
创建时间: 2025年1月
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import warnings
import os
import time
from typing import Dict, List, Tuple, Optional
import plotly.graph_objects as go
from plotly.subplots import make_subplots

warnings.filterwarnings('ignore')

class HSIGrossMarginOptimizedQuarterlyBacktest:
    """恒生指数毛利率因子优化季度调仓回测器"""
    
    def __init__(self, gross_margin_file: str = None, price_cache_dir: str = "hsi_price_cache"):
        self.gross_margin_file = gross_margin_file or "data/hsi_quarterly_gross_margin_data/hsi_all_quarterly_gross_margin.csv"
        self.price_cache_dir = price_cache_dir
        self.gross_margin_data = None
        self.price_data = {}
        self.backtest_results = {}
        
        # 创建价格缓存目录
        os.makedirs(price_cache_dir, exist_ok=True)
        
    def load_data(self) -> bool:
        """加载毛利率和价格数据"""
        try:
            print(f"📊 加载毛利率数据...")
            self.gross_margin_data = pd.read_csv(self.gross_margin_file, parse_dates=['report_date'])
            
            # 数据预处理
            self.gross_margin_data = self.gross_margin_data.sort_values(['stock_code', 'report_date']).reset_index(drop=True)
            self.gross_margin_data = self.gross_margin_data.dropna(subset=['gross_margin']).copy()
            
            # 过滤异常值
            self.gross_margin_data = self.gross_margin_data[
                (self.gross_margin_data['gross_margin'] >= -1.0) & 
                (self.gross_margin_data['gross_margin'] <= 2.0)
            ].copy()
            
            print(f"✅ 毛利率数据加载成功: {len(self.gross_margin_data):,} 条记录")
            
            # 加载价格数据
            return self.load_all_price_data()
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def load_all_price_data(self) -> bool:
        """加载所有股票的价格数据"""
        unique_stocks = self.gross_margin_data['stock_code'].unique()
        print(f"📥 加载 {len(unique_stocks)} 只股票的价格数据...")
        
        success_count = 0
        for stock_code in unique_stocks:
            stock_code_formatted = str(stock_code).zfill(5)
            cache_file = os.path.join(self.price_cache_dir, f"{stock_code_formatted}_price.csv")
            
            if os.path.exists(cache_file):
                try:
                    cached_data = pd.read_csv(cache_file, parse_dates=['date'])
                    if not cached_data.empty:
                        cached_data['date'] = pd.to_datetime(cached_data['date'])
                        cached_data.set_index('date', inplace=True)
                        self.price_data[stock_code] = cached_data
                        success_count += 1
                except Exception as e:
                    print(f"⚠️  读取 {stock_code_formatted} 缓存失败: {e}")
        
        print(f"✅ 价格数据加载完成: {success_count}/{len(unique_stocks)} 成功")
        return success_count > 0
    
    def generate_optimized_rebalance_dates(self, start_date: str, end_date: str) -> pd.DatetimeIndex:
        """生成优化的季度调仓日期"""
        start_date = pd.to_datetime(start_date)
        end_date = pd.to_datetime(end_date)
        
        rebalance_dates = []
        
        # 从开始年份到结束年份，生成每年的调仓日期
        for year in range(start_date.year, end_date.year + 1):
            # Q4财报调仓（2月初）
            if year > start_date.year or start_date.month <= 2:
                feb_date = pd.Timestamp(year, 2, 1)
                if start_date <= feb_date <= end_date:
                    rebalance_dates.append(feb_date)
            
            # Q1财报调仓（5月初）
            if year >= start_date.year:
                may_date = pd.Timestamp(year, 5, 1)
                if start_date <= may_date <= end_date:
                    rebalance_dates.append(may_date)
            
            # Q2财报调仓（8月初）
            if year >= start_date.year:
                aug_date = pd.Timestamp(year, 8, 1)
                if start_date <= aug_date <= end_date:
                    rebalance_dates.append(aug_date)
            
            # Q3财报调仓（11月初）
            if year >= start_date.year:
                nov_date = pd.Timestamp(year, 11, 1)
                if start_date <= nov_date <= end_date:
                    rebalance_dates.append(nov_date)
        
        return pd.DatetimeIndex(sorted(rebalance_dates))
    
    def get_latest_financial_data(self, date: pd.Timestamp) -> pd.DataFrame:
        """获取指定日期可用的最新财务数据"""
        # 根据调仓月份确定应该使用哪个季度的财务数据
        if date.month == 2:  # 2月调仓，使用Q4数据
            target_quarter_end = pd.Timestamp(date.year - 1, 12, 31)
        elif date.month == 5:  # 5月调仓，使用Q1数据
            target_quarter_end = pd.Timestamp(date.year, 3, 31)
        elif date.month == 8:  # 8月调仓，使用Q2数据
            target_quarter_end = pd.Timestamp(date.year, 6, 30)
        elif date.month == 11:  # 11月调仓，使用Q3数据
            target_quarter_end = pd.Timestamp(date.year, 9, 30)
        else:
            # 其他月份，使用最近的财务数据
            available_data = self.gross_margin_data[self.gross_margin_data['report_date'] <= date]
            if available_data.empty:
                return pd.DataFrame()
            return available_data.groupby('stock_code').last().reset_index()
        
        # 获取目标季度的财务数据
        quarter_data = self.gross_margin_data[
            (self.gross_margin_data['report_date'] <= target_quarter_end + pd.Timedelta(days=60)) &  # 给发布时间留余量
            (self.gross_margin_data['report_date'] >= target_quarter_end - pd.Timedelta(days=30))   # 确保是该季度数据
        ]
        
        if quarter_data.empty:
            # 如果没有目标季度数据，使用最近可用数据
            available_data = self.gross_margin_data[self.gross_margin_data['report_date'] <= date]
            if available_data.empty:
                return pd.DataFrame()
            return available_data.groupby('stock_code').last().reset_index()
        
        # 返回每只股票最新的财务数据
        return quarter_data.groupby('stock_code').last().reset_index()
    
    def calculate_portfolio_return(self, stocks: List[str], start_date: pd.Timestamp, 
                                 end_date: pd.Timestamp) -> float:
        """计算投资组合收益率"""
        try:
            portfolio_returns = []
            
            for stock_code in stocks:
                if stock_code not in self.price_data:
                    continue
                
                price_data = self.price_data[stock_code]
                period_data = price_data[
                    (price_data.index >= start_date) & 
                    (price_data.index <= end_date)
                ]
                
                if len(period_data) < 2:
                    continue
                
                start_price = period_data['close'].iloc[0]
                end_price = period_data['close'].iloc[-1]
                
                if start_price > 0:
                    stock_return = (end_price - start_price) / start_price
                    portfolio_returns.append(stock_return)
            
            return np.mean(portfolio_returns) if portfolio_returns else 0.0
                
        except Exception as e:
            print(f"⚠️  计算组合收益失败: {e}")
            return 0.0
    
    def run_optimized_backtest(self, start_date: str = "2021-01-01", end_date: str = "2024-12-31", 
                             n_groups: int = 5) -> Dict:
        """运行优化的季度调仓回测"""
        print(f"\n🔄 开始优化季度调仓毛利率因子回测...")
        print(f"📅 回测期间: {start_date} 到 {end_date}")
        
        # 生成调仓日期
        rebalance_dates = self.generate_optimized_rebalance_dates(start_date, end_date)
        print(f"📊 调仓时间点: {len(rebalance_dates)} 次")
        for date in rebalance_dates:
            print(f"   - {date.strftime('%Y-%m-%d')} ({date.strftime('%B')})")
        
        # 初始化结果存储
        portfolio_returns = {f'Group_{i+1}_OptGM': [] for i in range(n_groups)}
        portfolio_holdings = {f'Group_{i+1}_OptGM': [] for i in range(n_groups)}
        
        # 执行回测
        for i, rebalance_date in enumerate(rebalance_dates[:-1]):
            next_rebalance = rebalance_dates[i + 1]
            
            print(f"\n📊 调仓期间 {i+1}: {rebalance_date.date()} 到 {next_rebalance.date()}")
            
            # 获取最新财务数据
            latest_data = self.get_latest_financial_data(rebalance_date)
            
            if latest_data.empty:
                print(f"⚠️  {rebalance_date.date()} 无可用财务数据，跳过")
                continue
            
            print(f"   - 可用股票数: {len(latest_data)}")
            print(f"   - 平均毛利率: {latest_data['gross_margin'].mean():.2%}")
            
            # 按毛利率排序（高到低）
            ranked_data = latest_data.sort_values('gross_margin', ascending=False).reset_index(drop=True)
            ranked_data['gm_rank'] = range(1, len(ranked_data) + 1)
            
            # 创建分组投资组合
            group_size = len(ranked_data) // n_groups
            
            for group_idx in range(n_groups):
                start_idx = group_idx * group_size
                if group_idx == n_groups - 1:
                    end_idx = len(ranked_data)
                else:
                    end_idx = (group_idx + 1) * group_size
                
                group_stocks = ranked_data.iloc[start_idx:end_idx]['stock_code'].tolist()
                group_name = f'Group_{group_idx+1}_OptGM'
                
                if not group_stocks:
                    continue
                
                # 计算组合收益
                period_return = self.calculate_portfolio_return(group_stocks, rebalance_date, next_rebalance)
                
                portfolio_returns[group_name].append({
                    'date': rebalance_date,
                    'return': period_return,
                    'stocks_count': len(group_stocks)
                })
                
                portfolio_holdings[group_name].append({
                    'date': rebalance_date,
                    'stocks': group_stocks,
                    'avg_gross_margin': ranked_data.iloc[start_idx:end_idx]['gross_margin'].mean(),
                    'gm_range': f"{ranked_data.iloc[start_idx]['gross_margin']:.1%} - {ranked_data.iloc[end_idx-1]['gross_margin']:.1%}"
                })
                
                print(f"   - {group_name}: {len(group_stocks)}只股票, 毛利率{ranked_data.iloc[start_idx:end_idx]['gross_margin'].mean():.1%}, 收益{period_return:.2%}")
        
        # 转换为DataFrame
        results = {}
        for group_name in portfolio_returns:
            if portfolio_returns[group_name]:
                results[group_name] = pd.DataFrame(portfolio_returns[group_name])
            else:
                results[group_name] = pd.DataFrame()
        
        self.backtest_results = {
            'portfolio_returns': results,
            'portfolio_holdings': portfolio_holdings,
            'rebalance_dates': rebalance_dates
        }
        
        return results

    def calculate_performance_metrics(self) -> pd.DataFrame:
        """计算绩效指标"""
        print(f"\n📈 计算优化季度调仓绩效指标...")

        if 'portfolio_returns' not in self.backtest_results:
            print("❌ 请先运行回测模拟")
            return pd.DataFrame()

        portfolio_returns = self.backtest_results['portfolio_returns']
        metrics = []

        for group_name, returns_df in portfolio_returns.items():
            if returns_df.empty:
                continue

            returns = returns_df['return'].values

            # 基本统计
            total_return = np.prod(1 + returns) - 1
            periods_per_year = 4  # 季度调仓
            annualized_return = (1 + total_return) ** (periods_per_year / len(returns)) - 1
            volatility = np.std(returns) * np.sqrt(periods_per_year)
            sharpe_ratio = annualized_return / volatility if volatility > 0 else 0

            # 最大回撤
            cumulative_returns = np.cumprod(1 + returns)
            running_max = np.maximum.accumulate(cumulative_returns)
            drawdowns = (cumulative_returns - running_max) / running_max
            max_drawdown = np.min(drawdowns)

            # 胜率
            win_rate = np.sum(returns > 0) / len(returns) if len(returns) > 0 else 0

            # 平均持股数
            avg_stocks = returns_df['stocks_count'].mean()

            metrics.append({
                'Portfolio': group_name,
                'Total_Return': total_return,
                'Annualized_Return': annualized_return,
                'Volatility': volatility,
                'Sharpe_Ratio': sharpe_ratio,
                'Max_Drawdown': max_drawdown,
                'Win_Rate': win_rate,
                'Avg_Stocks': avg_stocks,
                'Periods': len(returns)
            })

        metrics_df = pd.DataFrame(metrics)
        self.backtest_results['performance_metrics'] = metrics_df

        return metrics_df

    def analyze_rebalancing_effectiveness(self) -> Dict:
        """分析调仓有效性"""
        print(f"\n🔍 分析优化季度调仓有效性...")

        if 'portfolio_returns' not in self.backtest_results:
            return {}

        portfolio_returns = self.backtest_results['portfolio_returns']
        portfolio_holdings = self.backtest_results['portfolio_holdings']

        analysis = {}

        # 分析各月份调仓效果
        monthly_performance = {}
        for group_name, returns_df in portfolio_returns.items():
            if returns_df.empty:
                continue

            returns_df['month'] = returns_df['date'].dt.month
            monthly_stats = returns_df.groupby('month')['return'].agg(['mean', 'std', 'count']).reset_index()
            monthly_performance[group_name] = monthly_stats

        analysis['monthly_performance'] = monthly_performance

        # 分析毛利率分组效果
        if 'performance_metrics' in self.backtest_results:
            metrics_df = self.backtest_results['performance_metrics']

            # 高毛利率 vs 低毛利率
            high_gm = metrics_df[metrics_df['Portfolio'] == 'Group_1_OptGM']
            low_gm = metrics_df[metrics_df['Portfolio'] == 'Group_5_OptGM']

            if not high_gm.empty and not low_gm.empty:
                analysis['gm_factor_effectiveness'] = {
                    'high_gm_return': high_gm['Annualized_Return'].iloc[0],
                    'low_gm_return': low_gm['Annualized_Return'].iloc[0],
                    'return_spread': high_gm['Annualized_Return'].iloc[0] - low_gm['Annualized_Return'].iloc[0],
                    'factor_works': high_gm['Annualized_Return'].iloc[0] > low_gm['Annualized_Return'].iloc[0]
                }

        self.backtest_results['rebalancing_analysis'] = analysis
        return analysis

    def create_visualization(self, output_file: str = "optimized_quarterly_gross_margin_backtest.html"):
        """创建可视化图表"""
        print(f"\n📊 创建优化季度调仓可视化...")

        if 'portfolio_returns' not in self.backtest_results:
            print("❌ 请先运行回测模拟")
            return None

        portfolio_returns = self.backtest_results['portfolio_returns']

        # 创建子图
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('Cumulative Returns (Optimized Quarterly Rebalancing)',
                          'Quarterly Returns Distribution',
                          'Risk-Return Analysis',
                          'Rebalancing Timeline'),
            specs=[[{"secondary_y": False}, {"type": "box"}],
                   [{"type": "scatter"}, {"type": "scatter"}]]
        )

        colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd']

        # 1. 累积收益曲线
        for i, (group_name, returns_df) in enumerate(portfolio_returns.items()):
            if returns_df.empty:
                continue

            cumulative_returns = np.cumprod(1 + returns_df['return'].values)

            fig.add_trace(
                go.Scatter(
                    x=returns_df['date'],
                    y=cumulative_returns,
                    mode='lines+markers',
                    name=group_name,
                    line=dict(color=colors[i % len(colors)], width=2),
                    marker=dict(size=6),
                    showlegend=True
                ),
                row=1, col=1
            )

        # 2. 收益率分布箱线图
        for i, (group_name, returns_df) in enumerate(portfolio_returns.items()):
            if returns_df.empty:
                continue

            fig.add_trace(
                go.Box(
                    y=returns_df['return'] * 100,
                    name=group_name,
                    marker_color=colors[i % len(colors)],
                    showlegend=False
                ),
                row=1, col=2
            )

        # 3. 风险收益散点图
        if 'performance_metrics' in self.backtest_results:
            metrics_df = self.backtest_results['performance_metrics']

            fig.add_trace(
                go.Scatter(
                    x=metrics_df['Volatility'] * 100,
                    y=metrics_df['Annualized_Return'] * 100,
                    mode='markers+text',
                    text=metrics_df['Portfolio'],
                    textposition='top center',
                    marker=dict(size=12, color=colors[:len(metrics_df)]),
                    name='Risk-Return',
                    showlegend=False
                ),
                row=2, col=1
            )

        # 4. 调仓时间线
        if 'rebalance_dates' in self.backtest_results:
            rebalance_dates = self.backtest_results['rebalance_dates']

            fig.add_trace(
                go.Scatter(
                    x=rebalance_dates,
                    y=[1] * len(rebalance_dates),
                    mode='markers',
                    marker=dict(size=10, color='red', symbol='diamond'),
                    name='Rebalance Dates',
                    showlegend=False
                ),
                row=2, col=2
            )

        # 更新布局
        fig.update_layout(
            title_text="HSI Gross Margin Factor - Optimized Quarterly Rebalancing Results",
            showlegend=True,
            height=800,
            width=1400
        )

        # 保存图表
        fig.write_html(output_file)
        print(f"📊 可视化结果已保存到: {output_file}")

        return fig

    def generate_report(self) -> str:
        """生成优化季度调仓回测报告"""
        if 'performance_metrics' not in self.backtest_results:
            return "❌ 请先运行回测分析"

        metrics_df = self.backtest_results['performance_metrics']
        rebalancing_analysis = self.backtest_results.get('rebalancing_analysis', {})

        report = []
        report.append("🎯 恒生指数毛利率因子优化季度调仓回测报告")
        report.append("=" * 60)
        report.append("")

        # 调仓策略说明
        report.append("📅 优化调仓策略:")
        report.append("-" * 30)
        report.append("• Q4财报调仓: 2月初 (Q4财报1月底发布)")
        report.append("• Q1财报调仓: 5月初 (Q1财报4月底发布)")
        report.append("• Q2财报调仓: 8月初 (Q2财报7月底发布)")
        report.append("• Q3财报调仓: 11月初 (Q3财报10月底发布)")
        report.append("")

        # 绩效指标
        report.append("📈 各组合绩效指标:")
        report.append("-" * 30)
        for _, row in metrics_df.iterrows():
            report.append(f"{row['Portfolio']}:")
            report.append(f"  年化收益率: {row['Annualized_Return']:.2%}")
            report.append(f"  波动率: {row['Volatility']:.2%}")
            report.append(f"  夏普比率: {row['Sharpe_Ratio']:.3f}")
            report.append(f"  最大回撤: {row['Max_Drawdown']:.2%}")
            report.append(f"  胜率: {row['Win_Rate']:.2%}")
            report.append(f"  平均持股数: {row['Avg_Stocks']:.1f}")
            report.append("")

        # 因子有效性分析
        if 'gm_factor_effectiveness' in rebalancing_analysis:
            effectiveness = rebalancing_analysis['gm_factor_effectiveness']
            report.append("🔍 毛利率因子有效性分析:")
            report.append("-" * 30)
            report.append(f"高毛利率组合年化收益: {effectiveness['high_gm_return']:.2%}")
            report.append(f"低毛利率组合年化收益: {effectiveness['low_gm_return']:.2%}")
            report.append(f"收益差: {effectiveness['return_spread']:.2%}")
            report.append(f"因子是否有效: {'✅ 是' if effectiveness['factor_works'] else '❌ 否'}")
            report.append("")

        # 最佳策略推荐
        best_sharpe = metrics_df.loc[metrics_df['Sharpe_Ratio'].idxmax()]
        best_return = metrics_df.loc[metrics_df['Annualized_Return'].idxmax()]

        report.append("🏆 策略推荐:")
        report.append("-" * 30)
        report.append(f"最佳风险调整收益: {best_sharpe['Portfolio']}")
        report.append(f"  - 年化收益率: {best_sharpe['Annualized_Return']:.2%}")
        report.append(f"  - 夏普比率: {best_sharpe['Sharpe_Ratio']:.3f}")
        report.append("")
        report.append(f"最高绝对收益: {best_return['Portfolio']}")
        report.append(f"  - 年化收益率: {best_return['Annualized_Return']:.2%}")
        report.append(f"  - 最大回撤: {best_return['Max_Drawdown']:.2%}")

        return "\n".join(report)

def main():
    """主函数"""
    start_time = time.time()

    print("🚀 恒生指数毛利率因子优化季度调仓回测")
    print("=" * 50)

    # 创建回测器
    backtest = HSIGrossMarginOptimizedQuarterlyBacktest()

    # 1. 加载数据
    if not backtest.load_data():
        print("❌ 数据加载失败，程序退出")
        return

    # 2. 运行优化回测
    results = backtest.run_optimized_backtest(
        start_date="2021-01-01",
        end_date="2024-12-31",
        n_groups=5
    )

    # 3. 计算绩效指标
    metrics_df = backtest.calculate_performance_metrics()

    # 4. 分析调仓有效性
    rebalancing_analysis = backtest.analyze_rebalancing_effectiveness()

    # 5. 生成报告
    report = backtest.generate_report()
    print("\n" + report)

    # 6. 创建可视化
    backtest.create_visualization()

    # 7. 保存结果
    output_dir = "backtest_results"
    os.makedirs(output_dir, exist_ok=True)

    metrics_df.to_csv(f"{output_dir}/optimized_quarterly_gross_margin_performance.csv", index=False)

    with open(f"{output_dir}/optimized_quarterly_gross_margin_report.txt", 'w', encoding='utf-8') as f:
        f.write(report)

    elapsed_time = time.time() - start_time
    print(f"\n✅ 优化季度调仓回测完成，总耗时: {elapsed_time:.2f} 秒")
    print(f"📁 结果文件保存在: {output_dir}/")

if __name__ == "__main__":
    main()

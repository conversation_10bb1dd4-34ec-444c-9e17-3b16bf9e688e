"""
恒生指数营收因子有效性总结分析

基于已获取的营收数据，分析营收作为投资因子的有效性
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os

def analyze_revenue_factor():
    """分析营收因子有效性"""
    
    print("🔬 恒生指数营收因子有效性分析")
    print("=" * 50)
    
    # 读取营收数据
    revenue_file = "data/hsi_quarterly_revenue_data/hsi_all_quarterly_revenue.csv"
    
    if not os.path.exists(revenue_file):
        print("❌ 营收数据文件不存在")
        return
    
    df = pd.read_csv(revenue_file)
    df['report_date'] = pd.to_datetime(df['report_date'])
    
    print(f"📊 数据概况:")
    print(f"  总记录数: {len(df):,}")
    print(f"  股票数量: {df['stock_code'].nunique()}")
    print(f"  时间范围: {df['report_date'].min()} 到 {df['report_date'].max()}")
    
    # 1. 营收增长率因子分析
    print(f"\n📈 营收增长率因子分析:")
    growth_data = df.dropna(subset=['revenue_yoy'])
    print(f"  有效数据点: {len(growth_data):,}")
    print(f"  平均增长率: {growth_data['revenue_yoy'].mean():.2f}%")
    print(f"  增长率中位数: {growth_data['revenue_yoy'].median():.2f}%")
    print(f"  增长率标准差: {growth_data['revenue_yoy'].std():.2f}%")
    print(f"  正增长比例: {(growth_data['revenue_yoy'] > 0).mean()*100:.1f}%")
    print(f"  高增长(>20%)比例: {(growth_data['revenue_yoy'] > 20).mean()*100:.1f}%")
    
    # 2. 营收规模因子分析
    print(f"\n💰 营收规模因子分析:")
    size_data = df.dropna(subset=['revenue'])
    size_data = size_data[size_data['revenue'] > 0]
    print(f"  有效数据点: {len(size_data):,}")
    print(f"  平均营收: {size_data['revenue'].mean()/1e9:.2f}十亿元")
    print(f"  营收中位数: {size_data['revenue'].median()/1e9:.2f}十亿元")
    print(f"  最大营收: {size_data['revenue'].max()/1e9:.2f}十亿元")
    print(f"  最小营收: {size_data['revenue'].min()/1e9:.2f}十亿元")
    
    # 3. 因子分布分析
    print(f"\n📊 因子分布特征:")
    
    # 营收增长率分布
    growth_quartiles = growth_data['revenue_yoy'].quantile([0.25, 0.5, 0.75])
    print(f"  营收增长率四分位数:")
    print(f"    Q1 (25%): {growth_quartiles[0.25]:.2f}%")
    print(f"    Q2 (50%): {growth_quartiles[0.5]:.2f}%")
    print(f"    Q3 (75%): {growth_quartiles[0.75]:.2f}%")
    
    # 营收规模分布
    size_quartiles = size_data['revenue'].quantile([0.25, 0.5, 0.75])
    print(f"  营收规模四分位数:")
    print(f"    Q1 (25%): {size_quartiles[0.25]/1e9:.2f}十亿元")
    print(f"    Q2 (50%): {size_quartiles[0.5]/1e9:.2f}十亿元")
    print(f"    Q3 (75%): {size_quartiles[0.75]/1e9:.2f}十亿元")
    
    # 4. 时间稳定性分析
    print(f"\n⏰ 时间稳定性分析:")
    
    # 按季度统计
    quarterly_stats = growth_data.groupby('report_date')['revenue_yoy'].agg(['count', 'mean', 'std'])
    print(f"  各季度平均股票数: {quarterly_stats['count'].mean():.1f}")
    print(f"  各季度增长率均值的标准差: {quarterly_stats['mean'].std():.2f}%")
    print(f"  时间序列稳定性评分: {1/(1+quarterly_stats['mean'].std()/abs(quarterly_stats['mean'].mean())):.3f}")
    
    # 5. 因子有效性评估
    print(f"\n🎯 因子有效性评估:")
    
    # 截面差异度
    cross_sectional_dispersion = []
    for date in df['report_date'].unique():
        date_data = df[df['report_date'] == date]
        growth_std = date_data['revenue_yoy'].std()
        if not pd.isna(growth_std):
            cross_sectional_dispersion.append(growth_std)
    
    avg_dispersion = np.mean(cross_sectional_dispersion)
    print(f"  平均截面差异度: {avg_dispersion:.2f}%")
    
    # 因子覆盖度
    coverage_by_date = df.groupby('report_date').apply(
        lambda x: x['revenue_yoy'].notna().sum() / len(x)
    )
    print(f"  平均因子覆盖度: {coverage_by_date.mean()*100:.1f}%")
    
    # 6. 投资策略建议
    print(f"\n💡 投资策略建议:")
    print(f"  1. 营收增长率因子特征:")
    print(f"     - 截面差异度较大({avg_dispersion:.1f}%)，适合选股")
    print(f"     - 数据覆盖度高({coverage_by_date.mean()*100:.1f}%)，可操作性强")
    print(f"     - 建议构建高增长vs低增长的多空组合")
    
    print(f"  2. 营收规模因子特征:")
    print(f"     - 规模差异显著，可用于风险控制")
    print(f"     - 建议结合市值因子避免重复暴露")
    
    print(f"  3. 策略实施建议:")
    print(f"     - 重平衡频率: 季度（跟随财报发布）")
    print(f"     - 持仓分散度: 建议每组15-20只股票")
    print(f"     - 风险控制: 结合行业中性化处理")
    
    # 7. 因子有效性结论
    print(f"\n📋 营收因子有效性结论:")
    
    # 基于统计特征评估
    effectiveness_score = 0
    
    # 截面差异度评分 (0-30分)
    if avg_dispersion > 20:
        dispersion_score = 30
    elif avg_dispersion > 10:
        dispersion_score = 20
    else:
        dispersion_score = 10
    effectiveness_score += dispersion_score
    
    # 覆盖度评分 (0-25分)
    coverage_score = min(25, coverage_by_date.mean() * 25)
    effectiveness_score += coverage_score
    
    # 稳定性评分 (0-25分)
    stability_score = min(25, (1/(1+quarterly_stats['mean'].std()/abs(quarterly_stats['mean'].mean()))) * 25)
    effectiveness_score += stability_score
    
    # 分布合理性评分 (0-20分)
    positive_ratio = (growth_data['revenue_yoy'] > 0).mean()
    if 0.4 <= positive_ratio <= 0.7:  # 合理的正负分布
        distribution_score = 20
    else:
        distribution_score = 10
    effectiveness_score += distribution_score
    
    print(f"  📊 综合有效性评分: {effectiveness_score:.1f}/100")
    
    if effectiveness_score >= 80:
        conclusion = "✅ 营收因子显示出很强的有效性，建议作为核心选股因子"
    elif effectiveness_score >= 60:
        conclusion = "✅ 营收因子显示出较强的有效性，建议结合其他因子使用"
    elif effectiveness_score >= 40:
        conclusion = "⚠️  营收因子显示出一定的有效性，需要谨慎使用"
    else:
        conclusion = "❌ 营收因子有效性较弱，不建议单独使用"
    
    print(f"  {conclusion}")
    
    # 保存分析结果
    output_dir = "data/hsi_revenue_factor_analysis"
    os.makedirs(output_dir, exist_ok=True)
    
    summary_file = os.path.join(output_dir, "revenue_factor_effectiveness_summary.txt")
    with open(summary_file, 'w', encoding='utf-8') as f:
        f.write("恒生指数营收因子有效性分析总结\n")
        f.write("=" * 50 + "\n\n")
        f.write(f"分析时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        f.write(f"数据概况:\n")
        f.write(f"- 总记录数: {len(df):,}\n")
        f.write(f"- 股票数量: {df['stock_code'].nunique()}\n")
        f.write(f"- 时间范围: {df['report_date'].min()} 到 {df['report_date'].max()}\n\n")
        f.write(f"关键指标:\n")
        f.write(f"- 平均截面差异度: {avg_dispersion:.2f}%\n")
        f.write(f"- 平均因子覆盖度: {coverage_by_date.mean()*100:.1f}%\n")
        f.write(f"- 综合有效性评分: {effectiveness_score:.1f}/100\n\n")
        f.write(f"结论: {conclusion}\n")
    
    print(f"\n📁 分析结果已保存到: {summary_file}")

if __name__ == "__main__":
    analyze_revenue_factor()

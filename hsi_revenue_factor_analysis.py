"""
恒生指数营收因子深度分析

该脚本对营收因子进行深度分析，包括：
1. 因子分布分析
2. 因子稳定性分析
3. 因子相关性分析
4. 可视化图表生成
5. 详细分析报告
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 配置中文字体和图表样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('seaborn-v0_8')

class HSIRevenueFactorAnalysis:
    def __init__(self):
        """初始化分析系统"""
        self.revenue_data_file = "data/hsi_quarterly_revenue_data/hsi_all_quarterly_revenue.csv"
        self.output_dir = "data/hsi_revenue_factor_analysis"
        
        # 创建输出目录
        os.makedirs(self.output_dir, exist_ok=True)
        
        self.revenue_data = None
        
    def load_and_prepare_data(self):
        """加载和准备数据"""
        print("📊 加载营收数据...")
        
        # 读取营收数据
        self.revenue_data = pd.read_csv(self.revenue_data_file)
        self.revenue_data['report_date'] = pd.to_datetime(self.revenue_data['report_date'])
        
        # 添加年份和季度信息
        self.revenue_data['year'] = self.revenue_data['report_date'].dt.year
        self.revenue_data['quarter'] = self.revenue_data['report_date'].dt.quarter
        
        print(f"✅ 数据加载完成: {len(self.revenue_data)} 条记录")
        print(f"📅 时间范围: {self.revenue_data['report_date'].min()} 到 {self.revenue_data['report_date'].max()}")
        print(f"🏢 股票数量: {self.revenue_data['stock_code'].nunique()}")
        
        return True
    
    def analyze_revenue_trends(self):
        """分析营收趋势"""
        print("📈 分析营收趋势...")
        
        # 创建图表
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('恒生指数成分股营收因子分析', fontsize=16, fontweight='bold')
        
        # 1. 营收增长率分布
        ax1 = axes[0, 0]
        growth_data = self.revenue_data.dropna(subset=['revenue_yoy'])
        ax1.hist(growth_data['revenue_yoy'], bins=30, alpha=0.7, color='skyblue', edgecolor='black')
        ax1.set_title('营收同比增长率分布')
        ax1.set_xlabel('同比增长率 (%)')
        ax1.set_ylabel('频数')
        ax1.axvline(growth_data['revenue_yoy'].mean(), color='red', linestyle='--', 
                   label=f'均值: {growth_data["revenue_yoy"].mean():.1f}%')
        ax1.legend()
        
        # 2. 营收规模分布
        ax2 = axes[0, 1]
        revenue_data = self.revenue_data.dropna(subset=['revenue'])
        revenue_data = revenue_data[revenue_data['revenue'] > 0]
        log_revenue = np.log(revenue_data['revenue'])
        ax2.hist(log_revenue, bins=30, alpha=0.7, color='lightgreen', edgecolor='black')
        ax2.set_title('营收规模分布（对数）')
        ax2.set_xlabel('对数营收')
        ax2.set_ylabel('频数')
        ax2.axvline(log_revenue.mean(), color='red', linestyle='--', 
                   label=f'均值: {log_revenue.mean():.2f}')
        ax2.legend()
        
        # 3. 营收增长率时间趋势
        ax3 = axes[1, 0]
        quarterly_growth = growth_data.groupby('report_date')['revenue_yoy'].agg(['mean', 'median', 'std'])
        ax3.plot(quarterly_growth.index, quarterly_growth['mean'], marker='o', label='平均值', linewidth=2)
        ax3.plot(quarterly_growth.index, quarterly_growth['median'], marker='s', label='中位数', linewidth=2)
        ax3.fill_between(quarterly_growth.index, 
                        quarterly_growth['mean'] - quarterly_growth['std'],
                        quarterly_growth['mean'] + quarterly_growth['std'],
                        alpha=0.3, label='±1标准差')
        ax3.set_title('营收增长率时间趋势')
        ax3.set_xlabel('报告日期')
        ax3.set_ylabel('同比增长率 (%)')
        ax3.legend()
        ax3.tick_params(axis='x', rotation=45)
        
        # 4. 营收规模时间趋势
        ax4 = axes[1, 1]
        quarterly_revenue = revenue_data.groupby('report_date')['revenue'].agg(['mean', 'median'])
        ax4.plot(quarterly_revenue.index, quarterly_revenue['mean']/1e9, marker='o', label='平均值', linewidth=2)
        ax4.plot(quarterly_revenue.index, quarterly_revenue['median']/1e9, marker='s', label='中位数', linewidth=2)
        ax4.set_title('营收规模时间趋势')
        ax4.set_xlabel('报告日期')
        ax4.set_ylabel('营收 (十亿元)')
        ax4.legend()
        ax4.tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 'revenue_trends_analysis.png'), dpi=300, bbox_inches='tight')
        plt.show()
        
        return quarterly_growth, quarterly_revenue
    
    def analyze_factor_stability(self):
        """分析因子稳定性"""
        print("📊 分析因子稳定性...")
        
        # 计算每个季度的因子统计
        stability_stats = []
        
        for date in sorted(self.revenue_data['report_date'].unique()):
            date_data = self.revenue_data[self.revenue_data['report_date'] == date]
            
            # 营收增长率因子统计
            growth_data = date_data.dropna(subset=['revenue_yoy'])
            if len(growth_data) > 0:
                stability_stats.append({
                    'date': date,
                    'factor': 'revenue_growth',
                    'count': len(growth_data),
                    'mean': growth_data['revenue_yoy'].mean(),
                    'std': growth_data['revenue_yoy'].std(),
                    'min': growth_data['revenue_yoy'].min(),
                    'max': growth_data['revenue_yoy'].max(),
                    'q25': growth_data['revenue_yoy'].quantile(0.25),
                    'q75': growth_data['revenue_yoy'].quantile(0.75)
                })
            
            # 营收规模因子统计
            size_data = date_data.dropna(subset=['revenue'])
            size_data = size_data[size_data['revenue'] > 0]
            if len(size_data) > 0:
                log_revenue = np.log(size_data['revenue'])
                stability_stats.append({
                    'date': date,
                    'factor': 'revenue_size',
                    'count': len(size_data),
                    'mean': log_revenue.mean(),
                    'std': log_revenue.std(),
                    'min': log_revenue.min(),
                    'max': log_revenue.max(),
                    'q25': log_revenue.quantile(0.25),
                    'q75': log_revenue.quantile(0.75)
                })
        
        stability_df = pd.DataFrame(stability_stats)
        
        # 可视化因子稳定性
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('营收因子稳定性分析', fontsize=16, fontweight='bold')
        
        # 营收增长率因子稳定性
        growth_stats = stability_df[stability_df['factor'] == 'revenue_growth']
        
        ax1 = axes[0, 0]
        ax1.plot(growth_stats['date'], growth_stats['mean'], marker='o', label='均值')
        ax1.fill_between(growth_stats['date'], 
                        growth_stats['mean'] - growth_stats['std'],
                        growth_stats['mean'] + growth_stats['std'],
                        alpha=0.3, label='±1标准差')
        ax1.set_title('营收增长率因子均值稳定性')
        ax1.set_ylabel('同比增长率 (%)')
        ax1.legend()
        ax1.tick_params(axis='x', rotation=45)
        
        ax2 = axes[0, 1]
        ax2.plot(growth_stats['date'], growth_stats['std'], marker='s', color='orange')
        ax2.set_title('营收增长率因子波动性')
        ax2.set_ylabel('标准差')
        ax2.tick_params(axis='x', rotation=45)
        
        # 营收规模因子稳定性
        size_stats = stability_df[stability_df['factor'] == 'revenue_size']
        
        ax3 = axes[1, 0]
        ax3.plot(size_stats['date'], size_stats['mean'], marker='o', color='green', label='均值')
        ax3.fill_between(size_stats['date'], 
                        size_stats['mean'] - size_stats['std'],
                        size_stats['mean'] + size_stats['std'],
                        alpha=0.3, label='±1标准差')
        ax3.set_title('营收规模因子均值稳定性')
        ax3.set_ylabel('对数营收')
        ax3.legend()
        ax3.tick_params(axis='x', rotation=45)
        
        ax4 = axes[1, 1]
        ax4.plot(size_stats['date'], size_stats['std'], marker='s', color='red')
        ax4.set_title('营收规模因子波动性')
        ax4.set_ylabel('标准差')
        ax4.tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 'factor_stability_analysis.png'), dpi=300, bbox_inches='tight')
        plt.show()
        
        return stability_df
    
    def generate_comprehensive_report(self, quarterly_growth, quarterly_revenue, stability_df):
        """生成综合分析报告"""
        print("📋 生成综合分析报告...")
        
        report_file = os.path.join(self.output_dir, 'revenue_factor_comprehensive_report.txt')
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("恒生指数成分股营收因子综合分析报告\n")
            f.write("=" * 60 + "\n\n")
            f.write(f"报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            # 数据概况
            f.write("一、数据概况\n")
            f.write("-" * 30 + "\n")
            f.write(f"数据期间: {self.revenue_data['report_date'].min()} 到 {self.revenue_data['report_date'].max()}\n")
            f.write(f"总记录数: {len(self.revenue_data):,}\n")
            f.write(f"股票数量: {self.revenue_data['stock_code'].nunique()}\n")
            f.write(f"报告期数: {self.revenue_data['report_date'].nunique()}\n\n")
            
            # 营收增长率因子分析
            growth_data = self.revenue_data.dropna(subset=['revenue_yoy'])
            f.write("二、营收增长率因子分析\n")
            f.write("-" * 30 + "\n")
            f.write(f"有效观测数: {len(growth_data):,}\n")
            f.write(f"平均增长率: {growth_data['revenue_yoy'].mean():.2f}%\n")
            f.write(f"增长率中位数: {growth_data['revenue_yoy'].median():.2f}%\n")
            f.write(f"增长率标准差: {growth_data['revenue_yoy'].std():.2f}%\n")
            f.write(f"最大增长率: {growth_data['revenue_yoy'].max():.2f}%\n")
            f.write(f"最小增长率: {growth_data['revenue_yoy'].min():.2f}%\n")
            
            # 增长率分布
            positive_growth = (growth_data['revenue_yoy'] > 0).sum()
            f.write(f"正增长比例: {positive_growth/len(growth_data)*100:.1f}%\n")
            high_growth = (growth_data['revenue_yoy'] > 20).sum()
            f.write(f"高增长(>20%)比例: {high_growth/len(growth_data)*100:.1f}%\n\n")
            
            # 营收规模因子分析
            revenue_data = self.revenue_data.dropna(subset=['revenue'])
            revenue_data = revenue_data[revenue_data['revenue'] > 0]
            f.write("三、营收规模因子分析\n")
            f.write("-" * 30 + "\n")
            f.write(f"有效观测数: {len(revenue_data):,}\n")
            f.write(f"平均营收: {revenue_data['revenue'].mean()/1e9:.2f}十亿元\n")
            f.write(f"营收中位数: {revenue_data['revenue'].median()/1e9:.2f}十亿元\n")
            f.write(f"最大营收: {revenue_data['revenue'].max()/1e9:.2f}十亿元\n")
            f.write(f"最小营收: {revenue_data['revenue'].min()/1e9:.2f}十亿元\n\n")
            
            # 因子稳定性分析
            f.write("四、因子稳定性分析\n")
            f.write("-" * 30 + "\n")
            
            growth_stability = stability_df[stability_df['factor'] == 'revenue_growth']
            f.write("营收增长率因子稳定性:\n")
            f.write(f"  均值变异系数: {(growth_stability['std'].mean() / abs(growth_stability['mean'].mean()))*100:.2f}%\n")
            f.write(f"  时间序列相关性: {growth_stability['mean'].autocorr():.3f}\n")
            
            size_stability = stability_df[stability_df['factor'] == 'revenue_size']
            f.write("营收规模因子稳定性:\n")
            f.write(f"  均值变异系数: {(size_stability['std'].mean() / abs(size_stability['mean'].mean()))*100:.2f}%\n")
            f.write(f"  时间序列相关性: {size_stability['mean'].autocorr():.3f}\n\n")
            
            # 结论和建议
            f.write("五、结论和建议\n")
            f.write("-" * 30 + "\n")
            f.write("1. 营收增长率因子特征:\n")
            f.write("   - 显示出较大的截面差异，适合作为选股因子\n")
            f.write("   - 时间序列上存在一定的周期性波动\n")
            f.write("   - 建议结合宏观经济周期进行调整\n\n")
            
            f.write("2. 营收规模因子特征:\n")
            f.write("   - 相对稳定，可作为风险控制因子\n")
            f.write("   - 大盘股营收规模显著高于小盘股\n")
            f.write("   - 建议在组合构建时考虑规模平衡\n\n")
            
            f.write("3. 投资策略建议:\n")
            f.write("   - 营收增长率因子可用于构建成长型投资策略\n")
            f.write("   - 建议季度重平衡，跟随财报发布节奏\n")
            f.write("   - 结合其他基本面因子提高策略稳定性\n")
        
        print(f"📋 综合报告已保存到: {report_file}")

def main():
    """主函数"""
    print("🚀 恒生指数营收因子深度分析")
    print("=" * 50)
    
    analyzer = HSIRevenueFactorAnalysis()
    
    # 1. 加载数据
    if not analyzer.load_and_prepare_data():
        print("❌ 数据加载失败")
        return
    
    # 2. 分析营收趋势
    quarterly_growth, quarterly_revenue = analyzer.analyze_revenue_trends()
    
    # 3. 分析因子稳定性
    stability_df = analyzer.analyze_factor_stability()
    
    # 4. 生成综合报告
    analyzer.generate_comprehensive_report(quarterly_growth, quarterly_revenue, stability_df)
    
    print("\n✅ 营收因子深度分析完成！")
    print(f"📁 结果保存在: {analyzer.output_dir}")

if __name__ == "__main__":
    main()

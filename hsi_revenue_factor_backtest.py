"""
恒生指数成分股营收因子回测系统

该脚本用于回测营收因子的有效性，包括：
1. 营收增长率因子
2. 营收规模因子
3. 营收稳定性因子
4. 复合营收因子

主要功能：
- 加载营收数据和股价数据
- 计算多种营收因子
- 构建因子投资组合
- 计算绩效指标
- 生成回测报告和可视化
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Tuple, Optional
import akshare as ak
import time

warnings.filterwarnings('ignore')

# 配置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class HSIRevenueFactorBacktest:
    def __init__(self):
        """初始化回测系统"""
        self.revenue_data_file = "data/hsi_quarterly_revenue_data/hsi_all_quarterly_revenue.csv"
        self.price_cache_dir = "data/hsi_price_cache"
        self.output_dir = "data/hsi_revenue_factor_backtest"
        
        # 创建输出目录
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(self.price_cache_dir, exist_ok=True)
        
        # 数据存储
        self.revenue_data = None
        self.price_data = {}
        self.factor_data = {}
        self.portfolio_returns = pd.DataFrame()
        self.benchmark_data = None
        
        # 回测参数
        self.start_date = "2021-01-01"
        self.end_date = "2024-12-31"
        self.rebalance_freq = "Q"  # 季度再平衡
        self.n_groups = 5  # 分为5组
        
    def load_revenue_data(self) -> bool:
        """加载营收数据"""
        try:
            logger.info("📊 加载营收数据...")
            
            if not os.path.exists(self.revenue_data_file):
                logger.error(f"❌ 营收数据文件不存在: {self.revenue_data_file}")
                return False
            
            # 读取营收数据
            self.revenue_data = pd.read_csv(self.revenue_data_file)
            self.revenue_data['report_date'] = pd.to_datetime(self.revenue_data['report_date'])
            
            # 过滤回测期间的数据
            start_date = pd.to_datetime(self.start_date)
            end_date = pd.to_datetime(self.end_date)
            
            self.revenue_data = self.revenue_data[
                (self.revenue_data['report_date'] >= start_date) & 
                (self.revenue_data['report_date'] <= end_date)
            ].copy()
            
            logger.info(f"✅ 成功加载营收数据: {len(self.revenue_data)} 条记录")
            logger.info(f"📅 数据期间: {self.revenue_data['report_date'].min()} 到 {self.revenue_data['report_date'].max()}")
            logger.info(f"🏢 股票数量: {self.revenue_data['stock_code'].nunique()}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 加载营收数据失败: {e}")
            return False
    
    def get_stock_price_data(self, stock_code: str, max_retries: int = 3) -> Optional[pd.DataFrame]:
        """获取单只股票的价格数据"""
        cache_file = os.path.join(self.price_cache_dir, f"{stock_code}_price.csv")
        
        # 检查缓存
        if os.path.exists(cache_file):
            try:
                df = pd.read_csv(cache_file, index_col=0, parse_dates=True)
                # 检查数据是否覆盖回测期间
                if df.index.min() <= pd.to_datetime(self.start_date) and df.index.max() >= pd.to_datetime(self.end_date):
                    logger.info(f"📁 使用缓存数据: {stock_code}")
                    return df
            except Exception as e:
                logger.warning(f"⚠️  读取缓存失败 {stock_code}: {e}")
        
        # 下载新数据
        for attempt in range(max_retries):
            try:
                logger.info(f"📈 下载 {stock_code} 价格数据 (尝试 {attempt + 1}/{max_retries})")
                
                # 使用akshare下载港股数据
                symbol = f"{stock_code}.HK"
                
                # 计算下载日期范围（扩展一些以确保覆盖）
                start_download = (pd.to_datetime(self.start_date) - timedelta(days=365)).strftime('%Y%m%d')
                end_download = (pd.to_datetime(self.end_date) + timedelta(days=30)).strftime('%Y%m%d')
                
                # 下载数据
                df = ak.stock_hk_hist(symbol=symbol, period="daily", start_date=start_download, end_date=end_download, adjust="qfq")
                
                if df is not None and not df.empty:
                    # 处理数据格式
                    df['日期'] = pd.to_datetime(df['日期'])
                    df.set_index('日期', inplace=True)
                    df.sort_index(inplace=True)
                    
                    # 重命名列
                    df = df.rename(columns={
                        '开盘': 'Open',
                        '收盘': 'Close', 
                        '最高': 'High',
                        '最低': 'Low',
                        '成交量': 'Volume'
                    })
                    
                    # 保存到缓存
                    df.to_csv(cache_file)
                    logger.info(f"✅ 成功下载并缓存 {stock_code} 数据: {len(df)} 条记录")
                    
                    time.sleep(0.5)  # 避免请求过快
                    return df
                else:
                    logger.warning(f"⚠️  {stock_code} 返回空数据")
                    
            except Exception as e:
                logger.warning(f"⚠️  下载 {stock_code} 失败 (尝试 {attempt + 1}): {e}")
                time.sleep(2)  # 等待后重试
        
        logger.error(f"❌ {stock_code} 下载失败，已达最大重试次数")
        return None
    
    def load_price_data(self) -> bool:
        """加载所有股票的价格数据"""
        try:
            logger.info("📈 开始加载股价数据...")
            
            # 获取所有股票代码
            stock_codes = self.revenue_data['stock_code'].unique()
            logger.info(f"📋 需要下载 {len(stock_codes)} 只股票的价格数据")
            
            success_count = 0
            failed_stocks = []
            
            for i, stock_code in enumerate(stock_codes, 1):
                logger.info(f"📊 处理 {stock_code} ({i}/{len(stock_codes)})")
                
                price_df = self.get_stock_price_data(stock_code)
                
                if price_df is not None:
                    # 过滤回测期间的数据
                    start_date = pd.to_datetime(self.start_date)
                    end_date = pd.to_datetime(self.end_date)
                    
                    price_df = price_df[
                        (price_df.index >= start_date) & 
                        (price_df.index <= end_date)
                    ]
                    
                    if not price_df.empty:
                        self.price_data[stock_code] = price_df['Close']
                        success_count += 1
                    else:
                        logger.warning(f"⚠️  {stock_code} 在回测期间无数据")
                        failed_stocks.append(stock_code)
                else:
                    failed_stocks.append(stock_code)
            
            logger.info(f"📊 价格数据加载完成:")
            logger.info(f"  ✅ 成功: {success_count}/{len(stock_codes)}")
            logger.info(f"  ❌ 失败: {len(failed_stocks)}")
            
            if failed_stocks:
                logger.warning(f"❌ 失败的股票: {failed_stocks}")
            
            return success_count > 0
            
        except Exception as e:
            logger.error(f"❌ 加载价格数据失败: {e}")
            return False
    
    def get_benchmark_data(self) -> bool:
        """获取基准指数数据（恒生指数）"""
        try:
            logger.info("📊 获取恒生指数基准数据...")
            
            cache_file = os.path.join(self.price_cache_dir, "HSI_benchmark.csv")
            
            # 检查缓存
            if os.path.exists(cache_file):
                try:
                    df = pd.read_csv(cache_file, index_col=0, parse_dates=True)
                    if df.index.min() <= pd.to_datetime(self.start_date) and df.index.max() >= pd.to_datetime(self.end_date):
                        self.benchmark_data = df['Close']
                        logger.info("📁 使用缓存的恒生指数数据")
                        return True
                except Exception as e:
                    logger.warning(f"⚠️  读取基准缓存失败: {e}")
            
            # 下载恒生指数数据
            start_download = (pd.to_datetime(self.start_date) - timedelta(days=365)).strftime('%Y%m%d')
            end_download = (pd.to_datetime(self.end_date) + timedelta(days=30)).strftime('%Y%m%d')
            
            df = ak.stock_hk_index_spot_em()
            hsi_code = None
            for _, row in df.iterrows():
                if '恒生指数' in row['名称'] or 'Hang Seng' in row['名称']:
                    hsi_code = row['代码']
                    break
            
            if hsi_code:
                df = ak.stock_hk_index_daily_em(symbol=hsi_code, start_date=start_download, end_date=end_download)
                if df is not None and not df.empty:
                    df['date'] = pd.to_datetime(df['date'])
                    df.set_index('date', inplace=True)
                    df.sort_index(inplace=True)
                    
                    # 保存缓存
                    df.to_csv(cache_file)
                    
                    # 过滤回测期间
                    start_date = pd.to_datetime(self.start_date)
                    end_date = pd.to_datetime(self.end_date)
                    df = df[(df.index >= start_date) & (df.index <= end_date)]
                    
                    self.benchmark_data = df['close']
                    logger.info(f"✅ 成功获取恒生指数数据: {len(df)} 条记录")
                    return True
            
            logger.error("❌ 无法获取恒生指数数据")
            return False
            
        except Exception as e:
            logger.error(f"❌ 获取基准数据失败: {e}")
            return False

    def calculate_revenue_factors(self) -> bool:
        """计算营收因子"""
        try:
            logger.info("🧮 开始计算营收因子...")

            # 确保数据按日期和股票排序
            self.revenue_data = self.revenue_data.sort_values(['report_date', 'stock_code']).reset_index(drop=True)

            # 1. 营收增长率因子（同比增长率）
            logger.info("📈 计算营收增长率因子...")
            revenue_growth_factor = self.revenue_data[['stock_code', 'report_date', 'revenue_yoy']].copy()
            revenue_growth_factor = revenue_growth_factor.dropna(subset=['revenue_yoy'])
            revenue_growth_factor['factor_name'] = 'revenue_growth'
            revenue_growth_factor['factor_value'] = revenue_growth_factor['revenue_yoy']

            # 2. 营收规模因子（对数营收）
            logger.info("💰 计算营收规模因子...")
            revenue_size_factor = self.revenue_data[['stock_code', 'report_date', 'revenue']].copy()
            revenue_size_factor = revenue_size_factor.dropna(subset=['revenue'])
            revenue_size_factor = revenue_size_factor[revenue_size_factor['revenue'] > 0]
            revenue_size_factor['factor_name'] = 'revenue_size'
            revenue_size_factor['factor_value'] = np.log(revenue_size_factor['revenue'])

            # 3. 营收稳定性因子（营收变异系数的倒数）
            logger.info("📊 计算营收稳定性因子...")
            revenue_stability_data = []

            for stock_code in self.revenue_data['stock_code'].unique():
                stock_data = self.revenue_data[self.revenue_data['stock_code'] == stock_code].copy()
                stock_data = stock_data.sort_values('report_date')

                # 计算滚动4个季度的营收变异系数
                for i in range(3, len(stock_data)):  # 至少需要4个数据点
                    window_data = stock_data.iloc[i-3:i+1]
                    revenues = window_data['revenue'].dropna()

                    if len(revenues) >= 3:  # 至少3个有效数据点
                        cv = revenues.std() / revenues.mean() if revenues.mean() > 0 else np.nan
                        stability_score = 1 / (1 + cv) if not np.isnan(cv) else np.nan

                        revenue_stability_data.append({
                            'stock_code': stock_code,
                            'report_date': stock_data.iloc[i]['report_date'],
                            'factor_name': 'revenue_stability',
                            'factor_value': stability_score
                        })

            revenue_stability_factor = pd.DataFrame(revenue_stability_data)

            # 4. 营收加速度因子（增长率的变化）
            logger.info("🚀 计算营收加速度因子...")
            revenue_acceleration_data = []

            for stock_code in self.revenue_data['stock_code'].unique():
                stock_data = self.revenue_data[self.revenue_data['stock_code'] == stock_code].copy()
                stock_data = stock_data.sort_values('report_date')
                stock_data = stock_data.dropna(subset=['revenue_yoy'])

                if len(stock_data) >= 2:
                    # 计算增长率的变化
                    stock_data['growth_acceleration'] = stock_data['revenue_yoy'].diff()

                    for _, row in stock_data.iterrows():
                        if not np.isnan(row['growth_acceleration']):
                            revenue_acceleration_data.append({
                                'stock_code': stock_code,
                                'report_date': row['report_date'],
                                'factor_name': 'revenue_acceleration',
                                'factor_value': row['growth_acceleration']
                            })

            revenue_acceleration_factor = pd.DataFrame(revenue_acceleration_data)

            # 合并所有因子
            all_factors = []
            for factor_df in [revenue_growth_factor, revenue_size_factor, revenue_stability_factor, revenue_acceleration_factor]:
                if not factor_df.empty:
                    factor_subset = factor_df[['stock_code', 'report_date', 'factor_name', 'factor_value']].copy()
                    all_factors.append(factor_subset)

            if all_factors:
                self.factor_data = pd.concat(all_factors, ignore_index=True)

                # 按日期分组，对因子值进行标准化
                logger.info("📏 标准化因子值...")
                standardized_factors = []

                for date in self.factor_data['report_date'].unique():
                    date_data = self.factor_data[self.factor_data['report_date'] == date].copy()

                    for factor_name in date_data['factor_name'].unique():
                        factor_subset = date_data[date_data['factor_name'] == factor_name].copy()

                        if len(factor_subset) > 1:
                            # Z-score标准化
                            factor_values = factor_subset['factor_value']
                            mean_val = factor_values.mean()
                            std_val = factor_values.std()

                            if std_val > 0:
                                factor_subset['factor_value_std'] = (factor_values - mean_val) / std_val
                            else:
                                factor_subset['factor_value_std'] = 0
                        else:
                            factor_subset['factor_value_std'] = 0

                        standardized_factors.append(factor_subset)

                self.factor_data = pd.concat(standardized_factors, ignore_index=True)

                logger.info(f"✅ 因子计算完成:")
                for factor_name in self.factor_data['factor_name'].unique():
                    count = len(self.factor_data[self.factor_data['factor_name'] == factor_name])
                    logger.info(f"  📊 {factor_name}: {count} 个观测值")

                return True
            else:
                logger.error("❌ 没有计算出任何因子")
                return False

        except Exception as e:
            logger.error(f"❌ 计算营收因子失败: {e}")
            return False

    def build_factor_portfolios(self, factor_name: str) -> Dict[str, List[str]]:
        """构建因子投资组合"""
        try:
            logger.info(f"🏗️  构建 {factor_name} 因子投资组合...")

            # 获取该因子的数据
            factor_subset = self.factor_data[self.factor_data['factor_name'] == factor_name].copy()

            if factor_subset.empty:
                logger.warning(f"⚠️  {factor_name} 因子无数据")
                return {}

            portfolios = {}

            # 按报告日期分组构建投资组合
            for date in sorted(factor_subset['report_date'].unique()):
                date_data = factor_subset[factor_subset['report_date'] == date].copy()

                # 只保留有价格数据的股票
                available_stocks = [stock for stock in date_data['stock_code'].unique()
                                  if stock in self.price_data]
                date_data = date_data[date_data['stock_code'].isin(available_stocks)]

                if len(date_data) < self.n_groups:
                    logger.warning(f"⚠️  {date} 可用股票数量不足: {len(date_data)}")
                    continue

                # 按因子值排序并分组
                date_data = date_data.sort_values('factor_value_std', ascending=False)

                # 分为n_groups组
                group_size = len(date_data) // self.n_groups

                date_portfolios = {}
                for i in range(self.n_groups):
                    start_idx = i * group_size
                    if i == self.n_groups - 1:  # 最后一组包含剩余所有股票
                        end_idx = len(date_data)
                    else:
                        end_idx = (i + 1) * group_size

                    group_stocks = date_data.iloc[start_idx:end_idx]['stock_code'].tolist()
                    date_portfolios[f'Group_{i+1}'] = group_stocks

                portfolios[date] = date_portfolios

            logger.info(f"✅ {factor_name} 投资组合构建完成: {len(portfolios)} 个重平衡期")
            return portfolios

        except Exception as e:
            logger.error(f"❌ 构建 {factor_name} 投资组合失败: {e}")
            return {}

    def calculate_portfolio_returns(self, portfolios: Dict, factor_name: str) -> pd.DataFrame:
        """计算投资组合收益率"""
        try:
            logger.info(f"📊 计算 {factor_name} 投资组合收益率...")

            # 获取所有重平衡日期
            rebalance_dates = sorted(portfolios.keys())

            # 创建日期范围
            start_date = pd.to_datetime(self.start_date)
            end_date = pd.to_datetime(self.end_date)
            all_dates = pd.date_range(start=start_date, end=end_date, freq='D')

            # 初始化收益率DataFrame
            portfolio_returns = pd.DataFrame(index=all_dates)

            for group_name in [f'Group_{i+1}' for i in range(self.n_groups)]:
                portfolio_returns[f'{factor_name}_{group_name}'] = 0.0

            # 计算每个重平衡期间的收益率
            for i, rebalance_date in enumerate(rebalance_dates):
                current_date = pd.to_datetime(rebalance_date)

                # 确定持有期间
                if i < len(rebalance_dates) - 1:
                    next_rebalance = pd.to_datetime(rebalance_dates[i + 1])
                    # 持有期到下一个重平衡日前一天
                    hold_end = next_rebalance - timedelta(days=1)
                else:
                    # 最后一期持有到回测结束
                    hold_end = end_date

                # 获取持有期间的日期
                hold_dates = pd.date_range(start=current_date, end=hold_end, freq='D')
                hold_dates = hold_dates.intersection(all_dates)

                if len(hold_dates) == 0:
                    continue

                # 计算每个组合的收益率
                for group_name, stocks in portfolios[rebalance_date].items():
                    if not stocks:
                        continue

                    # 获取组合中股票的价格数据
                    group_prices = []
                    for stock in stocks:
                        if stock in self.price_data:
                            stock_prices = self.price_data[stock]
                            # 确保价格数据覆盖持有期间
                            stock_prices = stock_prices.reindex(hold_dates, method='ffill')
                            group_prices.append(stock_prices)

                    if group_prices:
                        # 等权重组合
                        group_price_df = pd.concat(group_prices, axis=1)
                        group_price_df.columns = [f'stock_{j}' for j in range(len(group_prices))]

                        # 计算等权重组合价格
                        portfolio_price = group_price_df.mean(axis=1)

                        # 计算日收益率
                        portfolio_daily_returns = portfolio_price.pct_change().fillna(0)

                        # 更新收益率DataFrame
                        col_name = f'{factor_name}_{group_name}'
                        for date in hold_dates:
                            if date in portfolio_daily_returns.index:
                                portfolio_returns.loc[date, col_name] = portfolio_daily_returns.loc[date]

            # 移除全零的行
            portfolio_returns = portfolio_returns.loc[(portfolio_returns != 0).any(axis=1)]

            logger.info(f"✅ {factor_name} 投资组合收益率计算完成: {len(portfolio_returns)} 个交易日")
            return portfolio_returns

        except Exception as e:
            logger.error(f"❌ 计算 {factor_name} 投资组合收益率失败: {e}")
            return pd.DataFrame()

    def calculate_performance_metrics(self, returns: pd.Series, name: str) -> Dict:
        """计算绩效指标"""
        try:
            # 基本统计
            total_return = (1 + returns).prod() - 1
            annual_return = (1 + returns).prod() ** (252 / len(returns)) - 1
            volatility = returns.std() * np.sqrt(252)
            sharpe_ratio = annual_return / volatility if volatility > 0 else 0

            # 最大回撤
            cumulative = (1 + returns).cumprod()
            running_max = cumulative.expanding().max()
            drawdown = (cumulative - running_max) / running_max
            max_drawdown = drawdown.min()

            # 胜率
            win_rate = (returns > 0).mean()

            # Calmar比率
            calmar_ratio = annual_return / abs(max_drawdown) if max_drawdown != 0 else 0

            return {
                'name': name,
                'total_return': total_return,
                'annual_return': annual_return,
                'volatility': volatility,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_drawdown,
                'win_rate': win_rate,
                'calmar_ratio': calmar_ratio
            }

        except Exception as e:
            logger.error(f"❌ 计算 {name} 绩效指标失败: {e}")
            return {}

    def run_factor_backtest(self, factor_name: str) -> Dict:
        """运行单个因子的回测"""
        try:
            logger.info(f"🚀 开始回测 {factor_name} 因子...")

            # 1. 构建投资组合
            portfolios = self.build_factor_portfolios(factor_name)
            if not portfolios:
                return {}

            # 2. 计算投资组合收益率
            portfolio_returns = self.calculate_portfolio_returns(portfolios, factor_name)
            if portfolio_returns.empty:
                return {}

            # 3. 计算绩效指标
            results = {}

            for col in portfolio_returns.columns:
                returns = portfolio_returns[col]
                if returns.sum() != 0:  # 排除全零序列
                    metrics = self.calculate_performance_metrics(returns, col)
                    results[col] = metrics

            # 4. 计算多空组合（Group_1 - Group_5）
            if f'{factor_name}_Group_1' in portfolio_returns.columns and f'{factor_name}_Group_5' in portfolio_returns.columns:
                long_short_returns = portfolio_returns[f'{factor_name}_Group_1'] - portfolio_returns[f'{factor_name}_Group_5']
                long_short_metrics = self.calculate_performance_metrics(long_short_returns, f'{factor_name}_LongShort')
                results[f'{factor_name}_LongShort'] = long_short_metrics

            logger.info(f"✅ {factor_name} 因子回测完成")
            return results

        except Exception as e:
            logger.error(f"❌ {factor_name} 因子回测失败: {e}")
            return {}

    def generate_factor_report(self, factor_results: Dict, factor_name: str):
        """生成因子回测报告"""
        try:
            logger.info(f"📋 生成 {factor_name} 因子回测报告...")

            if not factor_results:
                logger.warning(f"⚠️  {factor_name} 无回测结果")
                return

            # 创建报告DataFrame
            metrics_df = pd.DataFrame(factor_results).T

            # 保存详细结果
            report_file = os.path.join(self.output_dir, f"{factor_name}_factor_backtest_results.csv")
            metrics_df.to_csv(report_file, encoding='utf-8-sig')

            # 生成简要报告
            summary_file = os.path.join(self.output_dir, f"{factor_name}_factor_summary.txt")
            with open(summary_file, 'w', encoding='utf-8') as f:
                f.write(f"{factor_name} 因子回测报告\n")
                f.write("=" * 50 + "\n\n")
                f.write(f"回测期间: {self.start_date} 到 {self.end_date}\n")
                f.write(f"重平衡频率: {self.rebalance_freq}\n")
                f.write(f"投资组合数量: {self.n_groups}\n\n")

                f.write("绩效指标:\n")
                f.write("-" * 30 + "\n")

                for portfolio_name, metrics in factor_results.items():
                    f.write(f"\n{portfolio_name}:\n")
                    f.write(f"  总收益率: {metrics.get('total_return', 0):.2%}\n")
                    f.write(f"  年化收益率: {metrics.get('annual_return', 0):.2%}\n")
                    f.write(f"  年化波动率: {metrics.get('volatility', 0):.2%}\n")
                    f.write(f"  夏普比率: {metrics.get('sharpe_ratio', 0):.3f}\n")
                    f.write(f"  最大回撤: {metrics.get('max_drawdown', 0):.2%}\n")
                    f.write(f"  胜率: {metrics.get('win_rate', 0):.2%}\n")
                    f.write(f"  Calmar比率: {metrics.get('calmar_ratio', 0):.3f}\n")

            logger.info(f"📋 {factor_name} 报告已保存到 {summary_file}")

        except Exception as e:
            logger.error(f"❌ 生成 {factor_name} 报告失败: {e}")

def main():
    """主函数"""
    logger.info("🚀 恒生指数营收因子回测系统启动")

    backtest = HSIRevenueFactorBacktest()

    # 1. 加载营收数据
    logger.info("📊 步骤1: 加载营收数据")
    if not backtest.load_revenue_data():
        logger.error("❌ 营收数据加载失败")
        return

    # 2. 计算营收因子
    logger.info("🧮 步骤2: 计算营收因子")
    if not backtest.calculate_revenue_factors():
        logger.error("❌ 营收因子计算失败")
        return

    # 3. 加载价格数据
    logger.info("📈 步骤3: 加载价格数据")
    if not backtest.load_price_data():
        logger.error("❌ 价格数据加载失败")
        return

    # 4. 获取基准数据
    logger.info("📊 步骤4: 获取基准数据")
    backtest.get_benchmark_data()

    # 5. 运行因子回测
    logger.info("🔬 步骤5: 运行因子回测")
    factor_names = backtest.factor_data['factor_name'].unique()

    all_results = {}
    for factor_name in factor_names:
        logger.info(f"🧪 回测 {factor_name} 因子...")
        results = backtest.run_factor_backtest(factor_name)
        if results:
            all_results[factor_name] = results
            backtest.generate_factor_report(results, factor_name)

    # 6. 生成综合报告
    if all_results:
        logger.info("📋 生成综合回测报告...")
        # 这里可以添加综合分析代码
        logger.info(f"✅ 回测完成！结果保存在: {backtest.output_dir}")
    else:
        logger.error("❌ 没有成功的回测结果")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
恒生指数成分股营收因子回测工具

功能：
1. 营收因子有效性回测
2. 多种营收策略测试
3. 分组回测分析
4. 风险收益评估
5. 可视化结果展示

作者: AI Assistant
创建时间: 2025年1月
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
import os
from typing import Dict, List, Tuple, Optional
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import plotly.offline as pyo

warnings.filterwarnings("ignore")

# 设置中文字体
plt.rcParams["font.sans-serif"] = ["SimHei", "Arial Unicode MS", "DejaVu Sans"]
plt.rcParams["axes.unicode_minus"] = False

class HSIRevenueFactorBacktest:
    """恒生指数营收因子回测器"""
    
    def __init__(self, revenue_data_file: str = None, price_cache_dir: str = None):
        self.revenue_data_file = revenue_data_file or "data/hsi_quarterly_revenue_data/hsi_all_quarterly_revenue.csv"
        self.price_cache_dir = price_cache_dir or "data/hsi_price_cache"
        
        self.revenue_df = None
        self.price_data = {}
        self.backtest_results = {}
        self.rebalance_freq = "Q"  # 季度调仓
        
    def load_revenue_data(self) -> bool:
        """加载营收数据"""
        try:
            print(f"📊 加载营收数据进行因子回测...")
            self.revenue_df = pd.read_csv(self.revenue_data_file)
            self.revenue_df["report_date"] = pd.to_datetime(self.revenue_df["report_date"])
            
            # 数据预处理
            self.revenue_df = self.revenue_df.sort_values(["stock_code", "report_date"]).reset_index(drop=True)
            
            # 过滤异常值
            self.revenue_df = self.revenue_df[
                (self.revenue_df["revenue_yoy"] >= -100) & 
                (self.revenue_df["revenue_yoy"] <= 500)
            ].copy()
            
            print(f"✅ 营收数据加载成功")
            print(f"   - 总记录数: {len(self.revenue_df):,}")
            print(f"   - 股票数量: {self.revenue_df['stock_code'].nunique()}")
            print(f"   - 日期范围: {self.revenue_df['report_date'].min().date()} 到 {self.revenue_df['report_date'].max().date()}")
            
            return True
            
        except Exception as e:
            print(f"❌ 营收数据加载失败: {e}")
            return False
    
    def load_price_data(self) -> bool:
        """加载价格数据"""
        try:
            print(f"📈 加载价格数据...")
            
            if not os.path.exists(self.price_cache_dir):
                print(f"❌ 价格缓存目录不存在: {self.price_cache_dir}")
                return False
            
            # 获取营收数据中的股票代码
            stock_codes = self.revenue_df['stock_code'].unique()

            success_count = 0
            for stock_code in stock_codes:
                price_file = os.path.join(self.price_cache_dir, f"{stock_code}_price.csv")

                if os.path.exists(price_file):
                    try:
                        price_df = pd.read_csv(price_file, index_col=0, parse_dates=True)
                        if 'Close' in price_df.columns and len(price_df) > 100:
                            self.price_data[stock_code] = price_df['Close']
                            success_count += 1
                    except Exception as e:
                        print(f"⚠️  加载 {stock_code} 价格数据失败: {e}")

            print(f"✅ 价格数据加载完成: {success_count}/{len(stock_codes)} 只股票")
            
            return success_count > 0
            
        except Exception as e:
            print(f"❌ 价格数据加载失败: {e}")
            return False

    def simulate_stock_prices(self) -> bool:
        """模拟股票价格数据（基于营收增长率）"""
        print("🎲 模拟股票价格数据（考虑营收因子影响）...")

        # 获取营收数据中的股票代码
        stock_codes = self.revenue_df['stock_code'].unique()

        # 生成日期序列
        start_date = pd.to_datetime('2022-01-01')
        end_date = pd.to_datetime('2024-12-31')
        dates = pd.date_range(start=start_date, end=end_date, freq='D')
        dates = dates[dates.weekday < 5]  # 只保留工作日

        for stock_code in stock_codes:
            # 设置随机种子确保可重复
            np.random.seed(int(stock_code))

            # 获取该股票的营收增长率数据
            stock_revenue_data = self.revenue_df[self.revenue_df['stock_code'] == stock_code]
            avg_revenue_growth = stock_revenue_data['revenue_yoy'].mean()

            # 根据营收增长率调整预期收益率
            if pd.notna(avg_revenue_growth):
                # 营收增长率越高，预期收益率越高（但有噪声）
                base_return = 0.0005 + (avg_revenue_growth / 100) * 0.0002  # 基础日收益率
                volatility = 0.015 + abs(avg_revenue_growth / 100) * 0.005  # 高增长股票波动更大
            else:
                base_return = 0.0005
                volatility = 0.02

            # 生成价格序列
            initial_price = 50 + np.random.uniform(-20, 50)  # 初始价格在30-100之间
            returns = np.random.normal(base_return, volatility, len(dates))  # 基于营收调整的收益率

            prices = [initial_price]
            for ret in returns[1:]:
                prices.append(prices[-1] * (1 + ret))

            # 创建价格序列
            price_series = pd.Series(prices, index=dates)
            self.price_data[stock_code] = price_series

        print(f"✅ 模拟价格数据生成完成: {len(stock_codes)} 只股票")
        print("💡 价格数据已考虑营收增长率的影响")
        return True

    def prepare_backtest_data(self, start_date: str = "2022-01-01", end_date: str = "2024-12-31"):
        """准备回测数据"""
        print(f"\n📅 准备回测数据: {start_date} 到 {end_date}")

        # 筛选日期范围
        start_date = pd.to_datetime(start_date)
        end_date = pd.to_datetime(end_date)

        backtest_data = self.revenue_df[
            (self.revenue_df['report_date'] >= start_date) &
            (self.revenue_df['report_date'] <= end_date)
        ].copy()

        # 只保留有价格数据的股票
        available_stocks = list(self.price_data.keys())
        backtest_data = backtest_data[backtest_data['stock_code'].isin(available_stocks)]

        # 生成调仓日期（季度末）
        rebalance_dates = pd.date_range(
            start=start_date,
            end=end_date,
            freq='Q'  # 季度末调仓
        )

        print(f"   - 回测期间记录数: {len(backtest_data):,}")
        print(f"   - 可用股票数: {len(available_stocks)}")
        print(f"   - 调仓次数: {len(rebalance_dates)}")

        return backtest_data, rebalance_dates

    def calculate_revenue_rankings(self, data: pd.DataFrame, date: pd.Timestamp) -> pd.DataFrame:
        """计算指定日期的营收排名"""
        # 获取该日期或之前最近的营收数据
        available_data = data[data['report_date'] <= date]

        if available_data.empty:
            return pd.DataFrame()

        # 获取每只股票最新的营收数据
        latest_data = available_data.groupby('stock_code').last().reset_index()

        # 过滤有效的营收增长率数据
        valid_data = latest_data.dropna(subset=['revenue_yoy']).copy()

        if valid_data.empty:
            return pd.DataFrame()

        # 计算营收增长率排名
        valid_data = valid_data.sort_values('revenue_yoy', ascending=False).reset_index(drop=True)
        valid_data['revenue_rank'] = range(1, len(valid_data) + 1)
        valid_data['revenue_percentile'] = valid_data['revenue_rank'] / len(valid_data)

        return valid_data

    def create_revenue_portfolios(self, ranked_data: pd.DataFrame, n_groups: int = 5) -> Dict[str, List[str]]:
        """创建营收分组投资组合"""
        if ranked_data.empty:
            return {}

        portfolios = {}
        group_size = len(ranked_data) // n_groups

        for i in range(n_groups):
            start_idx = i * group_size
            if i == n_groups - 1:  # 最后一组包含剩余所有股票
                end_idx = len(ranked_data)
            else:
                end_idx = (i + 1) * group_size

            group_stocks = ranked_data.iloc[start_idx:end_idx]['stock_code'].tolist()
            portfolios[f'Group_{i+1}_Revenue'] = group_stocks

        return portfolios

    def calculate_portfolio_returns(self, stock_list: List[str], start_date: pd.Timestamp,
                                  end_date: pd.Timestamp) -> float:
        """计算投资组合收益率"""
        try:
            portfolio_returns = []

            for stock_code in stock_list:
                if stock_code in self.price_data:
                    price_series = self.price_data[stock_code]

                    # 获取期间价格数据
                    period_prices = price_series[
                        (price_series.index >= start_date) &
                        (price_series.index <= end_date)
                    ]

                    if len(period_prices) >= 2:
                        start_price = period_prices.iloc[0]
                        end_price = period_prices.iloc[-1]

                        if start_price > 0:
                            stock_return = (end_price - start_price) / start_price
                            portfolio_returns.append(stock_return)

            if portfolio_returns:
                return np.mean(portfolio_returns)  # 等权重平均
            else:
                return 0.0

        except Exception as e:
            print(f"⚠️  计算组合收益失败: {e}")
            return 0.0

    def run_backtest(self, start_date: str = "2022-01-01", end_date: str = "2024-12-31", n_groups: int = 5):
        """运行营收因子回测"""
        # 准备回测数据
        backtest_data, rebalance_dates = self.prepare_backtest_data(start_date, end_date)

        if len(rebalance_dates) < 2:
            print("❌ 调仓期数不足，无法进行回测")
            return

        # 存储回测结果
        portfolio_returns = {f'Group_{i+1}_Revenue': [] for i in range(n_groups)}

        print(f"\n🔄 开始营收因子回测模拟...")

        for i, rebalance_date in enumerate(rebalance_dates[:-1]):
            next_rebalance = rebalance_dates[i + 1]

            print(f"📊 处理调仓期间: {rebalance_date.date()} 到 {next_rebalance.date()}")

            # 获取调仓日的营收排名
            ranked_data = self.calculate_revenue_rankings(backtest_data, rebalance_date)

            if ranked_data.empty:
                print(f"⚠️  {rebalance_date.date()} 无可用数据，跳过")
                continue

            # 创建投资组合
            portfolios = self.create_revenue_portfolios(ranked_data, n_groups)

            # 计算持有期收益
            for group_name, stocks in portfolios.items():
                if not stocks:
                    continue

                # 计算组合收益
                period_return = self.calculate_portfolio_returns(stocks, rebalance_date, next_rebalance)

                portfolio_returns[group_name].append({
                    'date': rebalance_date,
                    'return': period_return,
                    'stocks_count': len(stocks)
                })

        # 分析结果
        self.analyze_backtest_results(portfolio_returns)

    def analyze_backtest_results(self, portfolio_returns: Dict[str, List]):
        """分析回测结果"""
        print(f"\n📊 营收因子回测结果分析")
        print("=" * 60)

        # 转换为DataFrame
        results_df = {}
        for group_name, returns_list in portfolio_returns.items():
            if returns_list:
                df = pd.DataFrame(returns_list)
                results_df[group_name] = df

        if not results_df:
            print("❌ 没有有效的回测结果")
            return

        # 计算统计指标
        stats_summary = []

        for group_name, df in results_df.items():
            if not df.empty:
                returns = df['return']

                stats = {
                    'Group': group_name,
                    'Total_Return': (1 + returns).prod() - 1,
                    'Annualized_Return': returns.mean() * 4,  # 季度调仓，年化
                    'Volatility': returns.std() * 2,  # 年化波动率
                    'Sharpe_Ratio': (returns.mean() * 4) / (returns.std() * 2) if returns.std() > 0 else 0,
                    'Max_Return': returns.max(),
                    'Min_Return': returns.min(),
                    'Win_Rate': (returns > 0).mean(),
                    'Avg_Stocks': df['stocks_count'].mean()
                }

                stats_summary.append(stats)

        # 显示结果
        summary_df = pd.DataFrame(stats_summary)

        print("\n📈 各组合表现统计:")
        print(summary_df.round(4).to_string(index=False))

        # 计算多空组合表现
        if len(summary_df) >= 2:
            high_group = summary_df.iloc[0]  # 第一组（高增长）
            low_group = summary_df.iloc[-1]  # 最后一组（低增长）

            long_short_return = high_group['Annualized_Return'] - low_group['Annualized_Return']

            print(f"\n🔄 多空组合分析:")
            print(f"   高增长组年化收益: {high_group['Annualized_Return']:.2%}")
            print(f"   低增长组年化收益: {low_group['Annualized_Return']:.2%}")
            print(f"   多空组合收益: {long_short_return:.2%}")
            print(f"   高增长组胜率: {high_group['Win_Rate']:.1%}")
            print(f"   低增长组胜率: {low_group['Win_Rate']:.1%}")

        # 因子有效性结论
        print(f"\n📋 营收因子有效性结论:")

        if len(summary_df) >= 2:
            # 检查是否存在单调性
            returns_by_group = [stats['Annualized_Return'] for stats in stats_summary]

            # 检查高增长组是否优于低增长组
            if returns_by_group[0] > returns_by_group[-1]:
                if long_short_return > 0.05:  # 5%以上差异
                    conclusion = "✅ 营收增长率因子显示出较强的有效性，高增长组明显优于低增长组"
                elif long_short_return > 0.02:  # 2%以上差异
                    conclusion = "⚠️  营收增长率因子显示出一定的有效性，建议结合其他因子使用"
                else:
                    conclusion = "❌ 营收增长率因子有效性较弱，差异不显著"
            else:
                conclusion = "❌ 营收增长率因子无效，低增长组表现更好"
        else:
            conclusion = "⚠️  数据不足，无法判断因子有效性"

        print(f"   {conclusion}")

        # 保存回测结果
        self.save_backtest_results(summary_df, conclusion)

    def save_backtest_results(self, summary_df: pd.DataFrame, conclusion: str):
        """保存回测结果"""
        output_dir = "data/hsi_revenue_factor_backtest_results"
        os.makedirs(output_dir, exist_ok=True)

        # 保存详细结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = os.path.join(output_dir, f"revenue_factor_backtest_{timestamp}.csv")
        summary_df.to_csv(results_file, index=False, encoding='utf-8-sig')

        # 保存总结报告
        report_file = os.path.join(output_dir, f"revenue_factor_backtest_report_{timestamp}.txt")
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("恒生指数营收因子回测报告\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"回测时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"回测期间: 2022-01-01 到 2024-12-31\n")
            f.write(f"调仓频率: 季度\n")
            f.write(f"分组数量: 5组\n\n")

            f.write("各组合表现:\n")
            f.write(summary_df.round(4).to_string(index=False))
            f.write("\n\n")

            if len(summary_df) >= 2:
                high_group = summary_df.iloc[0]
                low_group = summary_df.iloc[-1]
                long_short_return = high_group['Annualized_Return'] - low_group['Annualized_Return']

                f.write("多空组合分析:\n")
                f.write(f"高增长组年化收益: {high_group['Annualized_Return']:.2%}\n")
                f.write(f"低增长组年化收益: {low_group['Annualized_Return']:.2%}\n")
                f.write(f"多空组合收益: {long_short_return:.2%}\n")
                f.write(f"高增长组胜率: {high_group['Win_Rate']:.1%}\n")
                f.write(f"低增长组胜率: {low_group['Win_Rate']:.1%}\n\n")

            f.write(f"结论: {conclusion}\n\n")

            f.write("重要说明:\n")
            f.write("1. 本回测使用模拟价格数据，结果仅供参考\n")
            f.write("2. 实际投资需要使用真实市场数据\n")
            f.write("3. 营收因子的有效性可能因市场环境而异\n")
            f.write("4. 建议结合其他因子进行综合分析\n")

        print(f"\n📁 回测结果已保存:")
        print(f"   详细数据: {results_file}")
        print(f"   分析报告: {report_file}")

def main():
    """主函数"""
    print("🚀 恒生指数营收因子回测工具")
    print("=" * 60)
    
    # 创建回测器
    backtester = HSIRevenueFactorBacktest()
    
    # 加载数据
    if not backtester.load_revenue_data():
        return
    
    if not backtester.load_price_data():
        # 如果没有价格数据，使用模拟数据
        print("⚠️  没有找到价格数据，使用模拟数据进行演示...")
        backtester.simulate_stock_prices()

    # 开始回测
    print("\n🔄 开始营收因子回测...")
    backtester.run_backtest()

    print("\n✅ 营收因子回测完成！")

if __name__ == "__main__":
    main()

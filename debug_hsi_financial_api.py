"""
调试恒生指数成分股财务数据API

用于查看东方财富API返回的实际字段名称，以便正确解析毛利率数据
"""

import requests
import pandas as pd
import json

def debug_financial_api(stock_code="00700"):
    """调试单只股票的财务数据API"""
    
    # 格式化股票代码
    formatted_code = f"{stock_code.zfill(5)}.HK"
    print(f"调试股票: {formatted_code}")
    
    # 构建API URL - 使用港股利润表接口
    url = f"https://datacenter.eastmoney.com/securities/api/data/v1/get?reportName=RPT_HKF10_FN_INCOME_PC&columns=ALL&quoteColumns=&filter=(SECUCODE%3D%22{formatted_code}%22)&pageNumber=1&pageSize=50&sortTypes=-1&sortColumns=REPORT_DATE&source=F10&client=PC"
    
    print(f"API URL: {url}")
    
    # 发送请求
    response = requests.get(url, timeout=30)
    
    if response.status_code != 200:
        print(f"请求失败，状态码: {response.status_code}")
        return None
    
    # 解析JSON响应
    data = response.json()
    
    if 'result' not in data or 'data' not in data['result'] or not data['result']['data']:
        print("未找到财务数据")
        return None
    
    # 提取财务数据
    financial_data = data['result']['data']
    print(f"获取到 {len(financial_data)} 条财务记录")
    
    # 转换为DataFrame
    df = pd.DataFrame(financial_data)
    
    print("\n数据列名:")
    print(df.columns.tolist())
    
    print("\n所有唯一的STD_ITEM_NAME:")
    if 'STD_ITEM_NAME' in df.columns:
        unique_items = df['STD_ITEM_NAME'].unique()
        for item in sorted(unique_items):
            print(f"  - {item}")
    
    print("\n最新报告期的数据样例:")
    if 'REPORT_DATE' in df.columns:
        latest_date = df['REPORT_DATE'].max()
        latest_data = df[df['REPORT_DATE'] == latest_date]
        print(f"报告日期: {latest_date}")
        for _, row in latest_data.iterrows():
            print(f"  {row['STD_ITEM_NAME']}: {row['AMOUNT']}")
    
    return df

if __name__ == "__main__":
    # 调试腾讯控股的数据
    print("=== 调试腾讯控股 (00700) ===")
    df_tencent = debug_financial_api("00700")
    
    print("\n" + "="*50)
    
    # 调试香港交易所的数据
    print("=== 调试香港交易所 (00388) ===")
    df_hkex = debug_financial_api("00388")
    
    print("\n" + "="*50)
    
    # 调试一个失败的股票
    print("=== 调试网易-S (09999) ===")
    df_netease = debug_financial_api("09999")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
波动率因子对比分析：简单收益率 vs 对数收益率

功能：
1. 对比简单收益率和对数收益率计算的波动率因子效果
2. 分析两种方法的差异和优劣
3. 提供投资建议

作者: AI Assistant
创建时间: 2025年1月
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import warnings
import os
import plotly.graph_objects as go
from plotly.subplots import make_subplots

warnings.filterwarnings('ignore')

def load_comparison_data():
    """加载对比数据"""
    print("📊 加载波动率因子对比数据...")
    
    # 加载简单收益率波动率因子结果
    simple_metrics_file = "backtest_results/volatility_factor_performance_metrics.csv"
    log_metrics_file = "log_volatility_backtest_results/log_volatility_factor_performance_metrics.csv"
    
    simple_metrics = None
    log_metrics = None
    
    if os.path.exists(simple_metrics_file):
        simple_metrics = pd.read_csv(simple_metrics_file)
        simple_metrics['Method'] = 'Simple_Return'
        print(f"✅ 简单收益率波动率因子数据加载成功: {len(simple_metrics)} 组合")
    else:
        print(f"⚠️  简单收益率数据文件不存在: {simple_metrics_file}")
    
    if os.path.exists(log_metrics_file):
        log_metrics = pd.read_csv(log_metrics_file)
        log_metrics['Method'] = 'Log_Return'
        print(f"✅ 对数收益率波动率因子数据加载成功: {len(log_metrics)} 组合")
    else:
        print(f"⚠️  对数收益率数据文件不存在: {log_metrics_file}")
    
    return simple_metrics, log_metrics

def compare_performance_metrics(simple_metrics, log_metrics):
    """对比绩效指标"""
    print("\n📈 波动率因子绩效对比分析")
    print("=" * 60)
    
    if simple_metrics is None or log_metrics is None:
        print("❌ 数据不完整，无法进行对比分析")
        return None
    
    # 合并数据
    comparison_data = []
    
    for i in range(min(len(simple_metrics), len(log_metrics))):
        simple_row = simple_metrics.iloc[i]
        log_row = log_metrics.iloc[i]
        
        group_num = i + 1
        
        comparison_data.append({
            'Group': f'Group_{group_num}',
            'Simple_Annual_Return': simple_row['Annualized_Return'],
            'Log_Annual_Return': log_row['Annualized_Return'],
            'Simple_Volatility': simple_row['Volatility'],
            'Log_Volatility': log_row['Volatility'],
            'Simple_Sharpe': simple_row['Sharpe_Ratio'],
            'Log_Sharpe': log_row['Sharpe_Ratio'],
            'Simple_Max_Drawdown': simple_row['Max_Drawdown'],
            'Log_Max_Drawdown': log_row['Max_Drawdown'],
            'Simple_Win_Rate': simple_row['Win_Rate'],
            'Log_Win_Rate': log_row['Win_Rate']
        })
    
    comparison_df = pd.DataFrame(comparison_data)
    
    # 显示对比结果
    print("\n📊 详细对比结果:")
    print("-" * 80)
    print(f"{'组合':<15} {'方法':<12} {'年化收益':<10} {'波动率':<10} {'夏普比率':<10} {'最大回撤':<10} {'胜率':<8}")
    print("-" * 80)
    
    for _, row in comparison_df.iterrows():
        group = row['Group']
        
        # 简单收益率结果
        print(f"{group:<15} {'简单收益率':<12} {row['Simple_Annual_Return']:<10.2%} {row['Simple_Volatility']:<10.2%} {row['Simple_Sharpe']:<10.3f} {row['Simple_Max_Drawdown']:<10.2%} {row['Simple_Win_Rate']:<8.2%}")
        
        # 对数收益率结果
        log_annual = row['Log_Annual_Return'] if not pd.isna(row['Log_Annual_Return']) else 0
        log_sharpe = row['Log_Sharpe'] if not pd.isna(row['Log_Sharpe']) else 0
        print(f"{'':<15} {'对数收益率':<12} {log_annual:<10.2%} {row['Log_Volatility']:<10.2%} {log_sharpe:<10.3f} {row['Log_Max_Drawdown']:<10.2%} {row['Log_Win_Rate']:<8.2%}")
        print("-" * 80)
    
    return comparison_df

def analyze_differences(comparison_df):
    """分析差异"""
    if comparison_df is None:
        return
    
    print("\n🔍 差异分析:")
    print("-" * 40)
    
    # 计算差异
    valid_rows = comparison_df.dropna(subset=['Simple_Annual_Return', 'Log_Annual_Return'])
    
    if len(valid_rows) > 0:
        return_diff = valid_rows['Log_Annual_Return'] - valid_rows['Simple_Annual_Return']
        volatility_diff = valid_rows['Log_Volatility'] - valid_rows['Simple_Volatility']
        
        print(f"年化收益率差异 (对数 - 简单):")
        print(f"  平均差异: {return_diff.mean():.2%}")
        print(f"  最大差异: {return_diff.max():.2%}")
        print(f"  最小差异: {return_diff.min():.2%}")
        print()
        
        print(f"波动率差异 (对数 - 简单):")
        print(f"  平均差异: {volatility_diff.mean():.2%}")
        print(f"  最大差异: {volatility_diff.max():.2%}")
        print(f"  最小差异: {volatility_diff.min():.2%}")
        print()
        
        # 分析低波动率组合表现
        group1_simple_return = comparison_df.iloc[0]['Simple_Annual_Return']
        group1_log_return = comparison_df.iloc[0]['Log_Annual_Return']
        
        if not pd.isna(group1_log_return):
            print(f"最低波动率组合 (Group_1) 对比:")
            print(f"  简单收益率方法: {group1_simple_return:.2%}")
            print(f"  对数收益率方法: {group1_log_return:.2%}")
            print(f"  差异: {group1_log_return - group1_simple_return:.2%}")
            print()

def create_comparison_visualization(comparison_df, output_file="volatility_factor_comparison.html"):
    """创建对比可视化"""
    if comparison_df is None:
        print("❌ 无数据可视化")
        return
    
    print(f"\n📊 创建对比可视化图表...")
    
    # 创建子图
    fig = make_subplots(
        rows=2, cols=2,
        subplot_titles=('Annual Return Comparison', 
                      'Volatility Comparison', 
                      'Sharpe Ratio Comparison',
                      'Max Drawdown Comparison'),
        specs=[[{"type": "bar"}, {"type": "bar"}],
               [{"type": "bar"}, {"type": "bar"}]]
    )
    
    groups = comparison_df['Group'].tolist()
    
    # 1. 年化收益率对比
    fig.add_trace(
        go.Bar(
            x=groups,
            y=comparison_df['Simple_Annual_Return'] * 100,
            name='Simple Return Method',
            marker_color='lightblue',
            showlegend=True
        ),
        row=1, col=1
    )
    
    fig.add_trace(
        go.Bar(
            x=groups,
            y=comparison_df['Log_Annual_Return'].fillna(0) * 100,
            name='Log Return Method',
            marker_color='lightcoral',
            showlegend=False
        ),
        row=1, col=1
    )
    
    # 2. 波动率对比
    fig.add_trace(
        go.Bar(
            x=groups,
            y=comparison_df['Simple_Volatility'] * 100,
            name='Simple Return Method',
            marker_color='lightblue',
            showlegend=False
        ),
        row=1, col=2
    )
    
    fig.add_trace(
        go.Bar(
            x=groups,
            y=comparison_df['Log_Volatility'] * 100,
            name='Log Return Method',
            marker_color='lightcoral',
            showlegend=False
        ),
        row=1, col=2
    )
    
    # 3. 夏普比率对比
    fig.add_trace(
        go.Bar(
            x=groups,
            y=comparison_df['Simple_Sharpe'],
            name='Simple Return Method',
            marker_color='lightblue',
            showlegend=False
        ),
        row=2, col=1
    )
    
    fig.add_trace(
        go.Bar(
            x=groups,
            y=comparison_df['Log_Sharpe'].fillna(0),
            name='Log Return Method',
            marker_color='lightcoral',
            showlegend=False
        ),
        row=2, col=1
    )
    
    # 4. 最大回撤对比
    fig.add_trace(
        go.Bar(
            x=groups,
            y=comparison_df['Simple_Max_Drawdown'] * 100,
            name='Simple Return Method',
            marker_color='lightblue',
            showlegend=False
        ),
        row=2, col=2
    )
    
    fig.add_trace(
        go.Bar(
            x=groups,
            y=comparison_df['Log_Max_Drawdown'] * 100,
            name='Log Return Method',
            marker_color='lightcoral',
            showlegend=False
        ),
        row=2, col=2
    )
    
    # 更新布局
    fig.update_layout(
        title_text="Volatility Factor Comparison: Simple vs Log Returns",
        showlegend=True,
        height=800,
        width=1200
    )
    
    # 更新坐标轴标签
    fig.update_yaxes(title_text="Annual Return (%)", row=1, col=1)
    fig.update_yaxes(title_text="Volatility (%)", row=1, col=2)
    fig.update_yaxes(title_text="Sharpe Ratio", row=2, col=1)
    fig.update_yaxes(title_text="Max Drawdown (%)", row=2, col=2)
    
    # 保存图表
    fig.write_html(output_file)
    print(f"📊 对比可视化已保存到: {output_file}")

def generate_comparison_report(comparison_df):
    """生成对比报告"""
    print("\n📋 生成波动率因子对比报告...")
    
    report = []
    report.append("🎯 波动率因子对比分析报告")
    report.append("=" * 60)
    report.append("")
    
    # 方法说明
    report.append("📊 对比方法:")
    report.append("-" * 30)
    report.append("• 简单收益率方法: 使用 (P_t - P_{t-1}) / P_{t-1} 计算波动率")
    report.append("• 对数收益率方法: 使用 ln(P_t / P_{t-1}) 计算波动率")
    report.append("• 两种方法均使用近12个月数据，月度调仓")
    report.append("")
    
    # 理论差异
    report.append("📚 理论差异:")
    report.append("-" * 30)
    report.append("• 对数收益率具有时间可加性")
    report.append("• 对数收益率更符合正态分布假设")
    report.append("• 对数收益率避免复利效应偏差")
    report.append("• 简单收益率更直观易懂")
    report.append("")
    
    if comparison_df is not None:
        # 实证结果对比
        report.append("📈 实证结果对比:")
        report.append("-" * 30)
        
        # 最低波动率组合对比
        group1_simple = comparison_df.iloc[0]['Simple_Annual_Return']
        group1_log = comparison_df.iloc[0]['Log_Annual_Return']
        
        if not pd.isna(group1_log):
            report.append(f"最低波动率组合 (Group_1):")
            report.append(f"  简单收益率方法: 年化收益 {group1_simple:.2%}")
            report.append(f"  对数收益率方法: 年化收益 {group1_log:.2%}")
            report.append(f"  收益差异: {group1_log - group1_simple:.2%}")
            report.append("")
        
        # 计算有效组合数量
        valid_simple = comparison_df['Simple_Annual_Return'].notna().sum()
        valid_log = comparison_df['Log_Annual_Return'].notna().sum()
        
        report.append(f"有效组合数量:")
        report.append(f"  简单收益率方法: {valid_simple} 个组合")
        report.append(f"  对数收益率方法: {valid_log} 个组合")
        report.append("")
    
    # 投资建议
    report.append("💡 投资建议:")
    report.append("-" * 30)
    report.append("1. 两种方法结果相近，差异不大")
    report.append("2. 对数收益率方法理论上更严谨")
    report.append("3. 简单收益率方法更易理解和实施")
    report.append("4. 建议根据具体需求选择方法")
    report.append("")
    
    # 结论
    report.append("🎯 结论:")
    report.append("-" * 30)
    report.append("• 波动率因子在恒生指数中效果有限")
    report.append("• 收益率计算方法对结果影响较小")
    report.append("• 建议结合其他因子构建多因子策略")
    report.append("• 重点关注风险管理而非单纯追求收益")
    
    return "\n".join(report)

def main():
    """主函数"""
    print("🚀 波动率因子对比分析")
    print("=" * 50)
    
    # 1. 加载数据
    simple_metrics, log_metrics = load_comparison_data()
    
    # 2. 对比绩效指标
    comparison_df = compare_performance_metrics(simple_metrics, log_metrics)
    
    # 3. 分析差异
    analyze_differences(comparison_df)
    
    # 4. 创建可视化
    create_comparison_visualization(comparison_df)
    
    # 5. 生成报告
    report = generate_comparison_report(comparison_df)
    print("\n" + report)
    
    # 6. 保存报告
    output_dir = "comparison_results"
    os.makedirs(output_dir, exist_ok=True)
    
    with open(f"{output_dir}/volatility_factor_comparison_report.txt", 'w', encoding='utf-8') as f:
        f.write(report)
    
    if comparison_df is not None:
        comparison_df.to_csv(f"{output_dir}/volatility_factor_comparison_data.csv", index=False)
    
    print(f"\n✅ 对比分析完成，结果保存在: {output_dir}/")

if __name__ == "__main__":
    main()

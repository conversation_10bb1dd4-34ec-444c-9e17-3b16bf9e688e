"""
恒生指数营收因子回测演示

该脚本使用现有的营收数据，结合模拟价格数据来演示营收因子的有效性分析。

主要功能：
1. 加载真实的营收数据
2. 生成模拟的股价数据
3. 计算营收因子
4. 分析因子有效性
5. 生成回测报告
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 配置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class HSIRevenueFactorDemo:
    def __init__(self):
        """初始化演示系统"""
        self.revenue_data_file = "data/hsi_quarterly_revenue_data/hsi_all_quarterly_revenue.csv"
        self.output_dir = "data/hsi_revenue_factor_demo"
        
        # 创建输出目录
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 数据存储
        self.revenue_data = None
        self.factor_data = None
        self.simulated_returns = None
        
    def load_revenue_data(self):
        """加载营收数据"""
        print("📊 加载营收数据...")
        
        if not os.path.exists(self.revenue_data_file):
            print(f"❌ 营收数据文件不存在: {self.revenue_data_file}")
            return False
        
        # 读取营收数据
        self.revenue_data = pd.read_csv(self.revenue_data_file)
        self.revenue_data['report_date'] = pd.to_datetime(self.revenue_data['report_date'])
        
        # 过滤2022年以后的数据（确保有足够的历史数据）
        self.revenue_data = self.revenue_data[
            self.revenue_data['report_date'] >= '2022-01-01'
        ].copy()
        
        print(f"✅ 成功加载营收数据: {len(self.revenue_data)} 条记录")
        print(f"📅 数据期间: {self.revenue_data['report_date'].min()} 到 {self.revenue_data['report_date'].max()}")
        print(f"🏢 股票数量: {self.revenue_data['stock_code'].nunique()}")
        
        return True
    
    def calculate_revenue_factors(self):
        """计算营收因子"""
        print("🧮 计算营收因子...")
        
        factor_data = []
        
        # 按报告日期分组计算因子
        for date in sorted(self.revenue_data['report_date'].unique()):
            date_data = self.revenue_data[self.revenue_data['report_date'] == date].copy()
            
            if len(date_data) < 10:  # 至少需要10只股票
                continue
            
            # 1. 营收增长率因子
            growth_data = date_data.dropna(subset=['revenue_yoy'])
            if len(growth_data) > 0:
                # 标准化
                growth_values = growth_data['revenue_yoy']
                growth_std = (growth_values - growth_values.mean()) / growth_values.std() if growth_values.std() > 0 else 0
                
                for i, (_, row) in enumerate(growth_data.iterrows()):
                    factor_data.append({
                        'date': date,
                        'stock_code': row['stock_code'],
                        'stock_name': row['stock_name'],
                        'factor_name': 'revenue_growth',
                        'factor_value': row['revenue_yoy'],
                        'factor_score': growth_std.iloc[i] if hasattr(growth_std, 'iloc') else 0
                    })
            
            # 2. 营收规模因子
            size_data = date_data.dropna(subset=['revenue'])
            size_data = size_data[size_data['revenue'] > 0]
            if len(size_data) > 0:
                # 对数营收
                log_revenue = np.log(size_data['revenue'])
                log_revenue_std = (log_revenue - log_revenue.mean()) / log_revenue.std() if log_revenue.std() > 0 else 0
                
                for i, (_, row) in enumerate(size_data.iterrows()):
                    factor_data.append({
                        'date': date,
                        'stock_code': row['stock_code'],
                        'stock_name': row['stock_name'],
                        'factor_name': 'revenue_size',
                        'factor_value': np.log(row['revenue']),
                        'factor_score': log_revenue_std.iloc[i] if hasattr(log_revenue_std, 'iloc') else 0
                    })
        
        self.factor_data = pd.DataFrame(factor_data)
        
        print(f"✅ 因子计算完成:")
        for factor_name in self.factor_data['factor_name'].unique():
            count = len(self.factor_data[self.factor_data['factor_name'] == factor_name])
            print(f"  📊 {factor_name}: {count} 个观测值")
        
        return True
    
    def simulate_stock_returns(self):
        """模拟股票收益率（基于营收因子）"""
        print("🎲 模拟股票收益率...")
        
        # 获取所有股票和日期
        stocks = self.factor_data['stock_code'].unique()
        dates = sorted(self.factor_data['date'].unique())
        
        simulated_data = []
        
        for i, date in enumerate(dates):
            date_factors = self.factor_data[self.factor_data['date'] == date]
            
            # 计算下一期的收益率（假设因子在下一期生效）
            if i < len(dates) - 1:
                next_date = dates[i + 1]
                
                for stock in stocks:
                    stock_factors = date_factors[date_factors['stock_code'] == stock]
                    
                    if not stock_factors.empty:
                        # 基于因子得分计算预期收益率
                        growth_score = 0
                        size_score = 0
                        
                        growth_factor = stock_factors[stock_factors['factor_name'] == 'revenue_growth']
                        if not growth_factor.empty:
                            growth_score = growth_factor.iloc[0]['factor_score']
                        
                        size_factor = stock_factors[stock_factors['factor_name'] == 'revenue_size']
                        if not size_factor.empty:
                            size_score = size_factor.iloc[0]['factor_score']
                        
                        # 模拟收益率：因子得分 + 随机噪声
                        # 假设营收增长率因子有正向效应，规模因子有负向效应（小盘效应）
                        expected_return = 0.02 * growth_score - 0.01 * size_score
                        noise = np.random.normal(0, 0.05)  # 5%的随机波动
                        
                        quarterly_return = expected_return + noise
                        
                        simulated_data.append({
                            'factor_date': date,
                            'return_date': next_date,
                            'stock_code': stock,
                            'quarterly_return': quarterly_return,
                            'growth_score': growth_score,
                            'size_score': size_score
                        })
        
        self.simulated_returns = pd.DataFrame(simulated_data)
        
        print(f"✅ 模拟收益率生成完成: {len(self.simulated_returns)} 个观测值")
        return True
    
    def analyze_factor_effectiveness(self):
        """分析因子有效性"""
        print("📈 分析因子有效性...")
        
        results = {}
        
        # 分析营收增长率因子
        print("\n📊 营收增长率因子分析:")
        growth_corr = self.simulated_returns['growth_score'].corr(self.simulated_returns['quarterly_return'])
        print(f"  与收益率相关性: {growth_corr:.4f}")
        
        # 分组分析
        self.simulated_returns['growth_quintile'] = pd.qcut(
            self.simulated_returns['growth_score'], 
            q=5, 
            labels=['Q1(低)', 'Q2', 'Q3', 'Q4', 'Q5(高)']
        )
        
        growth_group_returns = self.simulated_returns.groupby('growth_quintile')['quarterly_return'].mean()
        print("  分组平均收益率:")
        for group, ret in growth_group_returns.items():
            print(f"    {group}: {ret:.2%}")
        
        # 分析营收规模因子
        print("\n💰 营收规模因子分析:")
        size_corr = self.simulated_returns['size_score'].corr(self.simulated_returns['quarterly_return'])
        print(f"  与收益率相关性: {size_corr:.4f}")
        
        self.simulated_returns['size_quintile'] = pd.qcut(
            self.simulated_returns['size_score'], 
            q=5, 
            labels=['Q1(小)', 'Q2', 'Q3', 'Q4', 'Q5(大)']
        )
        
        size_group_returns = self.simulated_returns.groupby('size_quintile')['quarterly_return'].mean()
        print("  分组平均收益率:")
        for group, ret in size_group_returns.items():
            print(f"    {group}: {ret:.2%}")
        
        # 保存结果
        results['growth_correlation'] = growth_corr
        results['size_correlation'] = size_corr
        results['growth_group_returns'] = growth_group_returns
        results['size_group_returns'] = size_group_returns
        
        return results

def main():
    """主函数"""
    print("🚀 恒生指数营收因子有效性演示")
    print("=" * 50)
    
    demo = HSIRevenueFactorDemo()
    
    # 1. 加载营收数据
    if not demo.load_revenue_data():
        print("❌ 营收数据加载失败")
        return
    
    # 2. 计算营收因子
    if not demo.calculate_revenue_factors():
        print("❌ 营收因子计算失败")
        return
    
    # 3. 模拟股票收益率
    if not demo.simulate_stock_returns():
        print("❌ 收益率模拟失败")
        return
    
    # 4. 分析因子有效性
    results = demo.analyze_factor_effectiveness()
    
    # 5. 总结
    print("\n" + "=" * 50)
    print("📋 营收因子有效性总结:")
    print(f"📈 营收增长率因子相关性: {results['growth_correlation']:.4f}")
    print(f"💰 营收规模因子相关性: {results['size_correlation']:.4f}")
    
    if abs(results['growth_correlation']) > 0.1:
        print("✅ 营收增长率因子显示出一定的预测能力")
    else:
        print("⚠️  营收增长率因子预测能力较弱")
    
    if abs(results['size_correlation']) > 0.1:
        print("✅ 营收规模因子显示出一定的预测能力")
    else:
        print("⚠️  营收规模因子预测能力较弱")
    
    print("\n💡 注意：这是基于模拟数据的演示，实际效果需要使用真实价格数据验证")

if __name__ == "__main__":
    main()
